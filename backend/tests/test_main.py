"""
Basic tests for the main application.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch

from main import app


@pytest.fixture
def client():
    """Test client fixture."""
    return TestClient(app)


def test_root_endpoint(client):
    """Test the root endpoint."""
    with patch('app.core.database.init_database', new_callable=AsyncMock), \
         patch('app.core.database.check_database_connection', new_callable=AsyncMock, return_value=True), \
         patch('app.core.database.close_database', new_callable=AsyncMock):
        
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "Warehouse Management System API" in data["message"]


def test_health_endpoint(client):
    """Test the health check endpoint."""
    with patch('app.core.database.init_database', new_callable=AsyncMock), \
         patch('app.core.database.check_database_connection', new_callable=AsyncMock, return_value=True), \
         patch('app.core.database.close_database', new_callable=AsyncMock):
        
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "warehouse-management-api"


def test_openapi_docs(client):
    """Test that OpenAPI docs are accessible."""
    with patch('app.core.database.init_database', new_callable=AsyncMock), \
         patch('app.core.database.check_database_connection', new_callable=AsyncMock, return_value=True), \
         patch('app.core.database.close_database', new_callable=AsyncMock):
        
        response = client.get("/docs")
        assert response.status_code == 200
        
        response = client.get("/redoc")
        assert response.status_code == 200
        
        response = client.get("/api/v1/openapi.json")
        assert response.status_code == 200
