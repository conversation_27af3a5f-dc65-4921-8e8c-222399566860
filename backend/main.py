"""
FastAPI Warehouse Management Backend Application

This is the main entry point for the warehouse management system backend.
It provides RESTful APIs for managing warehouse operations including picks,
vehicle movements, operator events, inventory transactions, and shipments.
"""

import uvicorn
from app.api.v1.api import api_router
from app.core.config import settings
from app.core.lifespan import lifespan
from app.core.logging import setup_logging
from app.core.middleware import setup_middleware
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Setup structured logging
setup_logging()

# Create FastAPI application with comprehensive documentation
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="""
    ## Warehouse Management System API

    A comprehensive RESTful API for managing warehouse operations including:

    * **Picks** - Pick operations with status tracking and validation
    * **Vehicle Movements** - Vehicle tracking with route efficiency metrics
    * **Operator Events** - Staff activity logging and performance tracking
    * **Inventory Transactions** - Stock movements and adjustments
    * **Shipments** - Order processing and tracking
    * **Failed Messages** - Error tracking and retry mechanisms

    ### Features

    * **Async/Await Support** - High-performance async operations
    * **PostgreSQL Integration** - Robust database operations with connection pooling
    * **Comprehensive Filtering** - Advanced search and filtering capabilities
    * **Pagination** - Efficient data retrieval with pagination
    * **Error Handling** - Structured error responses and logging
    * **Validation** - Request/response validation with Pydantic
    * **Monitoring** - Health checks and performance metrics

    ### Authentication

    Currently, the API operates without authentication for development purposes.
    Production deployments should implement proper authentication and authorization.

    ### Rate Limiting

    No rate limiting is currently implemented. Consider adding rate limiting
    for production deployments.
    """,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
    contact={
        "name": "Warehouse Management System",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    openapi_tags=[
        {
            "name": "picks",
            "description": "Pick operations with status tracking, validation, and business logic",
        },
        {
            "name": "vehicle-movements",
            "description": "Vehicle tracking with route efficiency and safety metrics",
        },
        {
            "name": "operator-events",
            "description": "Staff activity logging and performance tracking",
        },
        {
            "name": "inventory-transactions",
            "description": "Stock movements, adjustments, and inventory operations",
        },
        {
            "name": "shipments",
            "description": "Order processing, tracking, and shipment management",
        },
        {
            "name": "failed-messages",
            "description": "Error tracking, retry mechanisms, and dead letter queue management",
        },
        {
            "name": "health",
            "description": "Health checks and system monitoring",
        },
    ],
)

# Setup middleware
setup_middleware(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    """Root endpoint providing basic API information."""
    return {
        "message": "Warehouse Management System API",
        "version": settings.VERSION,
        "docs_url": "/docs",
        "redoc_url": "/redoc",
    }


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {"status": "healthy", "service": "warehouse-management-api"}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
    )
