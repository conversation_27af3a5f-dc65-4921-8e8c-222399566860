# Warehouse Management System API

A comprehensive FastAPI backend application for warehouse management operations, built with modern async patterns and production-ready architecture.

## 🚀 Features

- **FastAPI Framework**: Modern, fast web framework with automatic API documentation
- **Async Database Operations**: PostgreSQL with asyncpg for high-performance async operations
- **Comprehensive Domain Models**: Complete warehouse entities (picks, vehicle movements, operator events, inventory transactions, shipments, failed messages)
- **Repository Pattern**: Clean separation of concerns with async repository layer
- **Service Layer**: Business logic implementation with validation and workflow management
- **Structured Logging**: JSON-formatted logging with request tracking and performance monitoring
- **Error Handling**: Comprehensive exception handling with proper HTTP status codes
- **API Documentation**: Auto-generated OpenAPI/Swagger documentation
- **Environment Configuration**: Flexible configuration management with environment variables
- **Testing Suite**: Comprehensive test coverage with pytest and async testing patterns

## 🏗️ Architecture

```
backend/
├── app/
│   ├── api/                    # API routes and endpoints
│   │   └── v1/
│   │       ├── endpoints/      # Individual endpoint modules
│   │       └── api.py         # Main API router
│   ├── core/                   # Core application components
│   │   ├── config.py          # Configuration management
│   │   ├── database.py        # Database connection and management
│   │   ├── exceptions.py      # Custom exception classes
│   │   ├── logging.py         # Structured logging setup
│   │   ├── middleware.py      # Request/response middleware
│   │   └── lifespan.py        # Application lifecycle management
│   ├── models/                 # Pydantic data models
│   ├── repositories/           # Database access layer
│   └── services/              # Business logic layer
├── tests/                     # Test suite
├── main.py                    # Application entry point
└── run.py                     # Development server runner
```

## 🛠️ Technology Stack

- **Framework**: FastAPI 0.104+
- **Database**: PostgreSQL with asyncpg driver
- **Package Manager**: uv (modern Python package manager)
- **Validation**: Pydantic v2 with comprehensive data validation
- **Logging**: structlog for structured JSON logging
- **Testing**: pytest with async support
- **Documentation**: Auto-generated OpenAPI/Swagger docs

## 📋 Prerequisites

- Python 3.12+
- PostgreSQL database (running externally)
- uv package manager

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone the repository
cd backend

# Install dependencies
uv sync

# Copy environment configuration
cp .env.example .env
# Edit .env with your database configuration
```

### 2. Database Configuration

Update `.env` file with your PostgreSQL connection details:

```env
POSTGRES_SERVER=localhost
POSTGRES_PORT=5433
POSTGRES_USER=warehouse
POSTGRES_PASSWORD=warehouse_pass
POSTGRES_DB=warehouse_db
```

### 3. Run the Application

```bash
# Using the development runner
uv run run.py

# Or directly with uvicorn
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. Access the API

- **API Base URL**: http://localhost:8000
- **Interactive Documentation**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **OpenAPI Schema**: http://localhost:8000/api/v1/openapi.json

## 🧪 Testing

```bash
# Run all tests
uv run pytest

# Run with verbose output
uv run pytest -v

# Run specific test file
uv run pytest tests/test_main.py

# Run with coverage
uv run pytest --cov=app
```

## 📊 API Endpoints

### Core Endpoints

- `GET /` - Root endpoint with API information
- `GET /health` - Health check endpoint
- `GET /docs` - Interactive API documentation
- `GET /redoc` - Alternative API documentation

### Warehouse Operations

#### Picks Management
- `POST /api/v1/picks/` - Create new pick
- `GET /api/v1/picks/{pick_id}` - Get pick by ID
- `PUT /api/v1/picks/{pick_id}` - Update pick
- `DELETE /api/v1/picks/{pick_id}` - Delete pick
- `GET /api/v1/picks/` - List picks with filtering and pagination
- `GET /api/v1/picks/statistics` - Get pick statistics
- `GET /api/v1/picks/operator/{operator_id}` - Get picks by operator
- `GET /api/v1/picks/batch/{batch_id}` - Get picks by batch

#### Other Modules
- Vehicle Movements: `/api/v1/vehicle-movements/`
- Operator Events: `/api/v1/operator-events/`
- Inventory Transactions: `/api/v1/inventory-transactions/`
- Shipments: `/api/v1/shipments/`
- Failed Messages: `/api/v1/failed-messages/`

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PROJECT_NAME` | Application name | "Warehouse Management System" |
| `VERSION` | Application version | "1.0.0" |
| `DEBUG` | Debug mode | false |
| `HOST` | Server host | 0.0.0.0 |
| `PORT` | Server port | 8000 |
| `POSTGRES_SERVER` | Database host | localhost |
| `POSTGRES_PORT` | Database port | 5433 |
| `POSTGRES_USER` | Database user | warehouse |
| `POSTGRES_PASSWORD` | Database password | warehouse_pass |
| `POSTGRES_DB` | Database name | warehouse_db |
| `LOG_LEVEL` | Logging level | INFO |
| `LOG_FORMAT` | Log format | json |

### Database Pool Settings

- `DB_POOL_MIN_SIZE`: Minimum pool connections (default: 5)
- `DB_POOL_MAX_SIZE`: Maximum pool connections (default: 20)
- `DB_POOL_MAX_QUERIES`: Max queries per connection (default: 50000)
- `DB_POOL_MAX_INACTIVE_CONNECTION_LIFETIME`: Connection timeout (default: 300.0)

## 🏭 Production Deployment

### Environment Setup

1. Set `DEBUG=false` in production
2. Configure proper database credentials
3. Set up proper logging aggregation
4. Configure CORS origins for your frontend
5. Set up monitoring and health checks

### Recommended Production Settings

```env
DEBUG=false
LOG_LEVEL=INFO
LOG_FORMAT=json
DB_POOL_MIN_SIZE=10
DB_POOL_MAX_SIZE=50
BACKEND_CORS_ORIGINS=https://your-frontend-domain.com
```

## 🔍 Monitoring and Logging

The application includes comprehensive structured logging:

- Request/response logging with timing
- Database operation logging
- Error tracking with stack traces
- Performance monitoring
- Health check endpoints

Logs are formatted as JSON for easy integration with log aggregation systems.

## 🤝 Development

### Code Structure

- **Models**: Pydantic schemas for data validation and serialization
- **Repositories**: Database access layer with async operations
- **Services**: Business logic and workflow management
- **API**: RESTful endpoints with proper error handling
- **Core**: Infrastructure components (database, logging, config)

### Adding New Features

1. Define Pydantic models in `app/models/`
2. Create repository class in `app/repositories/`
3. Implement business logic in `app/services/`
4. Add API endpoints in `app/api/v1/endpoints/`
5. Write tests in `tests/`

## 📝 License

This project is part of a warehouse management system implementation.

## 🆘 Support

For issues and questions:

1. Check the API documentation at `/docs`
2. Review the application logs
3. Verify database connectivity
4. Check environment configuration