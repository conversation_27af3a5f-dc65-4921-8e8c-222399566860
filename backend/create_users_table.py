#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the users table for authentication.
This script connects to the database and creates the users table with necessary indexes.
"""

import asyncio
import asyncpg
from app.core.config import settings
from app.core.auth import get_password_hash

async def create_users_table():
    """Create the users table and insert a default admin user."""
    
    # SQL to create users table
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        full_name VARCHAR(255),
        hashed_password VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL
    );
    """
    
    # SQL to create indexes
    create_indexes_sql = """
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
    CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
    """
    
    # SQL to create trigger function
    create_trigger_function_sql = """
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    """
    
    # SQL to create trigger
    create_trigger_sql = """
    DROP TRIGGER IF EXISTS update_users_updated_at ON users;
    CREATE TRIGGER update_users_updated_at 
        BEFORE UPDATE ON users 
        FOR EACH ROW 
        EXECUTE FUNCTION update_updated_at_column();
    """
    
    try:
        # Connect to database
        connection = await asyncpg.connect(str(settings.DATABASE_URL))
        
        print("Creating users table...")
        await connection.execute(create_table_sql)
        print("✓ Users table created")
        
        print("Creating indexes...")
        await connection.execute(create_indexes_sql)
        print("✓ Indexes created")
        
        print("Creating trigger function...")
        await connection.execute(create_trigger_function_sql)
        print("✓ Trigger function created")
        
        print("Creating trigger...")
        await connection.execute(create_trigger_sql)
        print("✓ Trigger created")
        
        # Check if admin user already exists
        admin_exists = await connection.fetchval(
            "SELECT 1 FROM users WHERE email = $1", 
            "<EMAIL>"
        )
        
        if not admin_exists:
            # Create default admin user
            admin_password_hash = get_password_hash("admin123")
            await connection.execute(
                """
                INSERT INTO users (email, full_name, hashed_password, is_active) 
                VALUES ($1, $2, $3, $4)
                """,
                "<EMAIL>",
                "System Administrator", 
                admin_password_hash,
                True
            )
            print("✓ Default admin user created")
            print("  Email: <EMAIL>")
            print("  Password: admin123")
        else:
            print("✓ Admin user already exists")
        
        await connection.close()
        print("\n✅ Database setup completed successfully!")
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(create_users_table())
