#!/usr/bin/env python3
"""
Development server runner for the warehouse management system.

This script provides a convenient way to run the development server
with proper configuration and error handling.
"""

import sys
import uvicorn
from app.core.config import settings


def main():
    """Run the development server."""
    try:
        print(f"Starting {settings.PROJECT_NAME} v{settings.VERSION}")
        print(f"Server will be available at: http://{settings.HOST}:{settings.PORT}")
        print(f"API documentation: http://{settings.HOST}:{settings.PORT}/docs")
        print(f"Debug mode: {settings.DEBUG}")
        print("-" * 50)
        
        uvicorn.run(
            "main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level="info" if not settings.DEBUG else "debug",
            access_log=True,
        )
        
    except KeyboardInterrupt:
        print("\nShutting down server...")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
