"""
Repository for operator events.
"""

import asyncpg

from app.core.database import DatabaseManager
from app.core.exceptions import DatabaseError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.operator_events import (
    OperatorEventCreate,
    OperatorEventFilterParams,
    OperatorEventResponse,
    OperatorEventUpdate,
)
from app.repositories.base import BaseRepository

logger = get_logger(__name__)


class OperatorEventRepository(
    BaseRepository[OperatorEventResponse, OperatorEventCreate, OperatorEventUpdate]
):
    """Repository for operator events."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, "operator_events", OperatorEventResponse)

    def _parse_record(self, record):
        """Parse database record and handle JSON fields."""
        import json

        record_dict = dict(record)

        # Parse JSON fields that are stored as strings
        json_fields = ["equipment_assigned", "performance_metrics", "certifications"]
        for field in json_fields:
            if field in record_dict and isinstance(record_dict[field], str):
                try:
                    record_dict[field] = json.loads(record_dict[field])
                except (json.JSONDecodeError, TypeError):
                    # If parsing fails, set to empty list/dict as appropriate
                    if field in ["equipment_assigned", "certifications"]:
                        record_dict[field] = []
                    else:
                        record_dict[field] = {}

        # Handle enum fields that might have invalid values
        if "role" in record_dict and record_dict["role"] not in [
            "picker",
            "driver",
            "supervisor",
            "trainer",
            "quality_control",
        ]:
            record_dict["role"] = "picker"  # Default to 'picker' for invalid values

        if "department" in record_dict and record_dict["department"] not in [
            "receiving",
            "storage",
            "picking",
            "packing",
            "shipping",
            "maintenance",
        ]:
            record_dict["department"] = (
                "picking"  # Default to 'picking' for invalid values
            )

        return record_dict

    async def list_with_filters(
        self, pagination: PaginationParams, filters: OperatorEventFilterParams
    ) -> PaginatedResponse:
        """List operator events with advanced filtering."""
        try:
            # Build WHERE conditions
            where_conditions = []
            where_values = []
            param_count = 0

            # Add filter conditions
            if filters.operator_id:
                param_count += 1
                where_conditions.append(f"operator_id = ${param_count}")
                where_values.append(filters.operator_id)

            if filters.employee_id:
                param_count += 1
                where_conditions.append(f"employee_id = ${param_count}")
                where_values.append(filters.employee_id)

            if filters.shift:
                param_count += 1
                where_conditions.append(f"shift = ${param_count}")
                where_values.append(filters.shift.value)

            if filters.department:
                param_count += 1
                where_conditions.append(f"department = ${param_count}")
                where_values.append(filters.department.value)

            if filters.role:
                param_count += 1
                where_conditions.append(f"role = ${param_count}")
                where_values.append(filters.role.value)

            if filters.activity_type:
                param_count += 1
                where_conditions.append(f"activity_type = ${param_count}")
                where_values.append(filters.activity_type.value)

            if filters.zone:
                param_count += 1
                where_conditions.append(f"zone = ${param_count}")
                where_values.append(filters.zone)

            if filters.supervisor_id:
                param_count += 1
                where_conditions.append(f"supervisor_id = ${param_count}")
                where_values.append(filters.supervisor_id)

            if filters.training_status:
                param_count += 1
                where_conditions.append(f"training_status = ${param_count}")
                where_values.append(filters.training_status.value)

            if filters.start_date:
                param_count += 1
                where_conditions.append(f"timestamp >= ${param_count}")
                where_values.append(filters.start_date)

            if filters.end_date:
                param_count += 1
                where_conditions.append(f"timestamp <= ${param_count}")
                where_values.append(filters.end_date)

            if filters.min_productivity is not None:
                param_count += 1
                where_conditions.append(f"productivity_score >= ${param_count}")
                where_values.append(filters.min_productivity)

            if filters.min_safety_score is not None:
                param_count += 1
                where_conditions.append(f"safety_score >= ${param_count}")
                where_values.append(filters.min_safety_score)

            if filters.search:
                param_count += 1
                where_conditions.append(
                    f"(operator_id ILIKE ${param_count} OR employee_id ILIKE ${param_count})"
                )
                search_term = f"%{filters.search}%"
                where_values.extend([search_term, search_term])
                param_count += 1  # Account for the second parameter

            where_clause = (
                f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            )

            # Build ORDER BY clause
            order_by = filters.sort_by or "timestamp"
            order_direction = "DESC" if filters.sort_order == "desc" else "ASC"
            order_clause = f"ORDER BY {order_by} {order_direction}"

            # Count total records
            count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_clause}"

            # Get paginated records
            offset = pagination.offset
            limit = pagination.size
            param_count += 1
            limit_param = param_count
            param_count += 1
            offset_param = param_count

            list_query = f"""
                SELECT * FROM {self.table_name}
                {where_clause}
                {order_clause}
                LIMIT ${limit_param} OFFSET ${offset_param}
            """

            async with self.db_manager.get_connection() as connection:
                # Get total count
                total = await connection.fetchval(count_query, *where_values)

                # Get records
                records = await connection.fetch(
                    list_query, *where_values, limit, offset
                )

            # Convert records to models
            items = [
                self.model_class.model_validate(self._parse_record(record))
                for record in records
            ]

            return PaginatedResponse.create(
                items=items,
                total=total,
                page=pagination.page,
                size=pagination.size,
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error listing operator events with filters: {e}")
            raise DatabaseError(f"Failed to list operator events: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error listing operator events with filters: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")

    async def get_operator_statistics(
        self, start_date=None, end_date=None, department=None
    ):
        """Get operator statistics."""
        try:
            # Build WHERE conditions for date filtering
            where_conditions = []
            where_values = []
            param_count = 0

            if start_date:
                param_count += 1
                where_conditions.append(f"timestamp >= ${param_count}")
                where_values.append(start_date)

            if end_date:
                param_count += 1
                where_conditions.append(f"timestamp <= ${param_count}")
                where_values.append(end_date)

            if department:
                param_count += 1
                where_conditions.append(f"department = ${param_count}")
                where_values.append(department)

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # Get basic statistics
            stats_query = f"""
                SELECT
                    COUNT(*) as total_events,
                    COUNT(DISTINCT operator_id) as active_operators,
                    AVG(productivity_score) as average_productivity,
                    AVG(safety_score) as average_safety_score,
                    SUM(hours_worked) as total_hours_worked,
                    SUM(overtime_hours) as total_overtime_hours
                FROM {self.table_name}
                {where_clause}
            """

            # Get department breakdown
            dept_query = f"""
                SELECT department, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY department
            """

            # Get shift breakdown
            shift_query = f"""
                SELECT shift, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY shift
            """

            # Get role breakdown
            role_query = f"""
                SELECT role, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY role
            """

            async with self.db_manager.get_connection() as connection:
                # Get basic stats
                stats_result = await connection.fetchrow(stats_query, *where_values)

                # Get breakdowns
                dept_results = await connection.fetch(dept_query, *where_values)
                shift_results = await connection.fetch(shift_query, *where_values)
                role_results = await connection.fetch(role_query, *where_values)

            # Build response
            from decimal import Decimal

            from app.models.operator_events import OperatorStatsResponse

            return OperatorStatsResponse(
                total_events=stats_result["total_events"] or 0,
                active_operators=stats_result["active_operators"] or 0,
                average_productivity=Decimal(
                    str(stats_result["average_productivity"] or 0)
                ),
                average_safety_score=Decimal(
                    str(stats_result["average_safety_score"] or 0)
                ),
                total_hours_worked=Decimal(
                    str(stats_result["total_hours_worked"] or 0)
                ),
                total_overtime_hours=Decimal(
                    str(stats_result["total_overtime_hours"] or 0)
                ),
                by_department={
                    str(row["department"]): row["count"] for row in dept_results
                },
                by_shift={str(row["shift"]): row["count"] for row in shift_results},
                by_role={str(row["role"]): row["count"] for row in role_results},
                training_compliance={},  # Can be enhanced later
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting operator statistics: {e}")
            raise DatabaseError(f"Failed to get operator statistics: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting operator statistics: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
