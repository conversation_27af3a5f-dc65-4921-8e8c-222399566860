"""
Repository for vehicle movements.

This module provides database operations specific to vehicle movement entities.
"""

import logging
from datetime import datetime
from typing import List, Optional

import asyncpg

from app.core.database import DatabaseManager
from app.core.exceptions import DatabaseError
from app.models.base import PaginatedResponse, PaginationParams
from app.models.vehicle_movements import (
    VehicleMovementCreate,
    VehicleMovementFilterParams,
    VehicleMovementResponse,
    VehicleMovementStatsResponse,
    VehicleMovementUpdate,
)
from app.repositories.base import BaseRepository

logger = logging.getLogger(__name__)


class VehicleMovementRepository(
    BaseRepository[
        VehicleMovementResponse, VehicleMovementCreate, VehicleMovementUpdate
    ]
):
    """Repository for vehicle movements."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, "vehicle_movements", VehicleMovementResponse)

    def _parse_record(self, record):
        """Parse database record and handle JSON fields."""
        import json

        record_dict = dict(record)

        # Parse JSON fields that are stored as strings
        json_fields = ["safety_incidents"]
        for field in json_fields:
            if field in record_dict and isinstance(record_dict[field], str):
                try:
                    record_dict[field] = json.loads(record_dict[field])
                except (json.JSONDecodeError, TypeError):
                    record_dict[field] = []

        # Handle battery level validation issue
        # The model has a validator that requires battery level to only increase during charging
        # For non-charging operations, ensure battery_level_end <= battery_level_start
        if (
            "battery_level_start" in record_dict
            and "battery_level_end" in record_dict
            and "movement_type" in record_dict
            and record_dict["movement_type"] != "charging"
        ):
            if (
                record_dict["battery_level_end"] is not None
                and record_dict["battery_level_start"] is not None
                and record_dict["battery_level_end"]
                > record_dict["battery_level_start"]
            ):
                # For non-charging operations, battery should decrease or stay same
                # Set battery_level_end to be equal to or less than start level
                record_dict["battery_level_end"] = max(
                    0, record_dict["battery_level_start"] - 5
                )

        return record_dict

    async def list_with_filters(
        self, pagination: PaginationParams, filters: VehicleMovementFilterParams
    ) -> PaginatedResponse:
        """List vehicle movements with advanced filtering."""
        try:
            # Build WHERE conditions
            where_conditions = []
            where_values = []
            param_count = 0

            # Add filter conditions
            if filters.vehicle_id:
                param_count += 1
                where_conditions.append(f"vehicle_id = ${param_count}")
                where_values.append(filters.vehicle_id)

            if filters.vehicle_type:
                param_count += 1
                where_conditions.append(f"vehicle_type = ${param_count}")
                where_values.append(filters.vehicle_type.value)

            if filters.operator_id:
                param_count += 1
                where_conditions.append(f"operator_id = ${param_count}")
                where_values.append(filters.operator_id)

            if filters.movement_type:
                param_count += 1
                where_conditions.append(f"movement_type = ${param_count}")
                where_values.append(filters.movement_type.value)

            if filters.start_date:
                param_count += 1
                where_conditions.append(f"start_time >= ${param_count}")
                where_values.append(filters.start_date)

            if filters.end_date:
                param_count += 1
                where_conditions.append(f"start_time <= ${param_count}")
                where_values.append(filters.end_date)

            if filters.search:
                param_count += 1
                where_conditions.append(
                    f"(vehicle_id ILIKE ${param_count} OR movement_id ILIKE ${param_count})"
                )
                search_term = f"%{filters.search}%"
                where_values.extend([search_term, search_term])
                param_count += 1  # Account for the second parameter

            where_clause = (
                f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            )

            # Build ORDER BY clause
            order_by = filters.sort_by or "start_time"
            order_direction = "DESC" if filters.sort_order == "desc" else "ASC"
            order_clause = f"ORDER BY {order_by} {order_direction}"

            # Count total records
            count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_clause}"

            # Get paginated records
            offset = pagination.offset
            limit = pagination.size
            param_count += 1
            limit_param = param_count
            param_count += 1
            offset_param = param_count

            list_query = f"""
                SELECT * FROM {self.table_name}
                {where_clause}
                {order_clause}
                LIMIT ${limit_param} OFFSET ${offset_param}
            """

            async with self.db_manager.get_connection() as connection:
                # Get total count
                total = await connection.fetchval(count_query, *where_values)

                # Get records
                records = await connection.fetch(
                    list_query, *where_values, limit, offset
                )

            # Convert records to models
            items = [
                self.model_class.model_validate(self._parse_record(record))
                for record in records
            ]

            return PaginatedResponse.create(
                items=items,
                total=total,
                page=pagination.page,
                size=pagination.size,
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error listing vehicle movements with filters: {e}")
            raise DatabaseError(f"Failed to list vehicle movements: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error listing vehicle movements with filters: {e}"
            )
            raise DatabaseError(f"Unexpected error: {str(e)}")

    async def get_by_movement_id(
        self, movement_id: str
    ) -> Optional[VehicleMovementResponse]:
        """Get a movement by movement_id."""
        return await self.get_by_field("movement_id", movement_id)

    async def get_movements_by_vehicle(
        self, vehicle_id: str, limit: int = 100
    ) -> List[VehicleMovementResponse]:
        """Get movements for a specific vehicle."""
        try:
            query = f"""
                SELECT * FROM {self.table_name}
                WHERE vehicle_id = $1
                ORDER BY start_time DESC
                LIMIT $2
            """

            async with self.db_manager.get_connection() as connection:
                records = await connection.fetch(query, vehicle_id, limit)

            return [self.model_class.model_validate(dict(record)) for record in records]

        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting movements by vehicle: {e}")
            raise DatabaseError(f"Failed to get movements by vehicle: {str(e)}")

    async def get_movements_by_operator(
        self, operator_id: str, limit: int = 100
    ) -> List[VehicleMovementResponse]:
        """Get movements for a specific operator."""
        try:
            query = f"""
                SELECT * FROM {self.table_name}
                WHERE operator_id = $1
                ORDER BY start_time DESC
                LIMIT $2
            """

            async with self.db_manager.get_connection() as connection:
                records = await connection.fetch(query, operator_id, limit)

            return [self.model_class.model_validate(dict(record)) for record in records]

        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting movements by operator: {e}")
            raise DatabaseError(f"Failed to get movements by operator: {str(e)}")

    async def get_movement_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        vehicle_id: Optional[str] = None,
    ) -> VehicleMovementStatsResponse:
        """Get movement statistics."""
        try:
            # Build WHERE conditions
            where_conditions = []
            where_values = []
            param_count = 0

            if start_date:
                param_count += 1
                where_conditions.append(f"start_time >= ${param_count}")
                where_values.append(start_date)

            if end_date:
                param_count += 1
                where_conditions.append(f"start_time <= ${param_count}")
                where_values.append(end_date)

            if vehicle_id:
                param_count += 1
                where_conditions.append(f"vehicle_id = ${param_count}")
                where_values.append(vehicle_id)

            where_clause = (
                f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            )

            query = f"""
                SELECT 
                    COUNT(*) as total_movements,
                    COALESCE(SUM(distance_meters), 0) as total_distance,
                    COALESCE(SUM(duration_seconds), 0) as total_duration,
                    AVG(average_speed_mps) as average_speed,
                    AVG(fuel_consumption_liters) as fuel_efficiency,
                    AVG(route_efficiency) as route_efficiency_avg,
                    COUNT(*) FILTER (WHERE array_length(safety_incidents, 1) > 0) as safety_incidents_count
                FROM {self.table_name}
                {where_clause}
            """

            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, *where_values)

            return VehicleMovementStatsResponse(
                total_movements=record["total_movements"] or 0,
                total_distance=record["total_distance"] or 0,
                total_duration=record["total_duration"] or 0,
                average_speed=float(record["average_speed"])
                if record["average_speed"]
                else None,
                fuel_efficiency=float(record["fuel_efficiency"])
                if record["fuel_efficiency"]
                else None,
                route_efficiency_avg=float(record["route_efficiency_avg"])
                if record["route_efficiency_avg"]
                else None,
                safety_incidents_count=record["safety_incidents_count"] or 0,
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting movement statistics: {e}")
            raise DatabaseError(f"Failed to get movement statistics: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting movement statistics: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
