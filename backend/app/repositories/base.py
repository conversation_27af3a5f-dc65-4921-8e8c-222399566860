"""
Base repository class with common database operations.

This module provides a base repository class that implements common CRUD operations
and can be extended by specific entity repositories.
"""

import logging
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic
from datetime import datetime

import asyncpg
from pydantic import BaseModel

from app.core.database import DatabaseManager
from app.core.exceptions import DatabaseError, NotFoundError
from app.models.base import PaginationParams, PaginatedResponse

logger = logging.getLogger(__name__)

# Type variables for generic repository
ModelType = TypeVar("ModelType", bound=BaseModel)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """Base repository class with common database operations."""
    
    def __init__(self, db_manager: DatabaseManager, table_name: str, model_class: Type[ModelType]):
        self.db_manager = db_manager
        self.table_name = table_name
        self.model_class = model_class
    
    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """Create a new record."""
        try:
            # Convert Pydantic model to dict
            data = obj_in.model_dump(exclude_unset=True)
            
            # Build INSERT query
            columns = list(data.keys())
            placeholders = [f"${i+1}" for i in range(len(columns))]
            values = list(data.values())
            
            query = f"""
                INSERT INTO {self.table_name} ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
                RETURNING *
            """
            
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, *values)
                
            if not record:
                raise DatabaseError(f"Failed to create record in {self.table_name}")
            
            return self.model_class.model_validate(dict(record))
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error creating record in {self.table_name}: {e}")
            raise DatabaseError(f"Failed to create record: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating record in {self.table_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def get_by_id(self, record_id: int) -> Optional[ModelType]:
        """Get a record by ID."""
        try:
            query = f"SELECT * FROM {self.table_name} WHERE id = $1"
            
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, record_id)
            
            if not record:
                return None
            
            return self.model_class.model_validate(dict(record))
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting record from {self.table_name}: {e}")
            raise DatabaseError(f"Failed to get record: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting record from {self.table_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def get_by_field(self, field_name: str, field_value: Any) -> Optional[ModelType]:
        """Get a record by a specific field."""
        try:
            query = f"SELECT * FROM {self.table_name} WHERE {field_name} = $1"
            
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, field_value)
            
            if not record:
                return None
            
            return self.model_class.model_validate(dict(record))
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting record from {self.table_name}: {e}")
            raise DatabaseError(f"Failed to get record: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting record from {self.table_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def update(self, record_id: int, obj_in: UpdateSchemaType) -> ModelType:
        """Update a record."""
        try:
            # Convert Pydantic model to dict, excluding unset fields
            data = obj_in.model_dump(exclude_unset=True)
            
            if not data:
                # No fields to update, return current record
                existing = await self.get_by_id(record_id)
                if not existing:
                    raise NotFoundError(self.table_name, str(record_id))
                return existing
            
            # Add updated_at timestamp
            data["updated_at"] = datetime.utcnow()
            
            # Build UPDATE query
            set_clauses = [f"{key} = ${i+2}" for i, key in enumerate(data.keys())]
            values = [record_id] + list(data.values())
            
            query = f"""
                UPDATE {self.table_name}
                SET {', '.join(set_clauses)}
                WHERE id = $1
                RETURNING *
            """
            
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, *values)
            
            if not record:
                raise NotFoundError(self.table_name, str(record_id))
            
            return self.model_class.model_validate(dict(record))
            
        except NotFoundError:
            raise
        except asyncpg.PostgresError as e:
            logger.error(f"Database error updating record in {self.table_name}: {e}")
            raise DatabaseError(f"Failed to update record: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error updating record in {self.table_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def delete(self, record_id: int) -> bool:
        """Delete a record."""
        try:
            query = f"DELETE FROM {self.table_name} WHERE id = $1"
            
            async with self.db_manager.get_connection() as connection:
                result = await connection.execute(query, record_id)
            
            # Check if any rows were affected
            return result.split()[-1] == "1"
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error deleting record from {self.table_name}: {e}")
            raise DatabaseError(f"Failed to delete record: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error deleting record from {self.table_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def list_with_pagination(
        self,
        pagination: PaginationParams,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_direction: str = "ASC"
    ) -> PaginatedResponse:
        """List records with pagination and optional filtering."""
        try:
            # Build WHERE clause
            where_conditions = []
            where_values = []
            
            if filters:
                for i, (key, value) in enumerate(filters.items(), 1):
                    if value is not None:
                        where_conditions.append(f"{key} = ${i}")
                        where_values.append(value)
            
            where_clause = f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            
            # Build ORDER BY clause
            order_clause = ""
            if order_by:
                direction = "DESC" if order_direction.upper() == "DESC" else "ASC"
                order_clause = f"ORDER BY {order_by} {direction}"
            
            # Count total records
            count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_clause}"
            
            # Get paginated records
            offset = pagination.offset
            limit = pagination.size
            
            list_query = f"""
                SELECT * FROM {self.table_name}
                {where_clause}
                {order_clause}
                LIMIT ${len(where_values) + 1} OFFSET ${len(where_values) + 2}
            """
            
            async with self.db_manager.get_connection() as connection:
                # Get total count
                total = await connection.fetchval(count_query, *where_values)
                
                # Get records
                records = await connection.fetch(list_query, *where_values, limit, offset)
            
            # Convert records to models
            items = [self.model_class.model_validate(dict(record)) for record in records]
            
            return PaginatedResponse.create(
                items=items,
                total=total,
                page=pagination.page,
                size=pagination.size,
            )
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error listing records from {self.table_name}: {e}")
            raise DatabaseError(f"Failed to list records: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error listing records from {self.table_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count records with optional filtering."""
        try:
            where_conditions = []
            where_values = []
            
            if filters:
                for i, (key, value) in enumerate(filters.items(), 1):
                    if value is not None:
                        where_conditions.append(f"{key} = ${i}")
                        where_values.append(value)
            
            where_clause = f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            query = f"SELECT COUNT(*) FROM {self.table_name} {where_clause}"
            
            async with self.db_manager.get_connection() as connection:
                count = await connection.fetchval(query, *where_values)
            
            return count
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error counting records in {self.table_name}: {e}")
            raise DatabaseError(f"Failed to count records: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error counting records in {self.table_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def exists(self, field_name: str, field_value: Any) -> bool:
        """Check if a record exists with the given field value."""
        try:
            query = f"SELECT 1 FROM {self.table_name} WHERE {field_name} = $1 LIMIT 1"
            
            async with self.db_manager.get_connection() as connection:
                result = await connection.fetchval(query, field_value)
            
            return result is not None
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error checking existence in {self.table_name}: {e}")
            raise DatabaseError(f"Failed to check existence: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error checking existence in {self.table_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
