"""
Repository for failed messages.
"""

import json

import asyncpg

from app.core.database import DatabaseManager
from app.core.exceptions import DatabaseError, NotFoundError, ValidationError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.failed_messages import (
    FailedMessageCreate,
    FailedMessageFilterParams,
    FailedMessageResponse,
    FailedMessageUpdate,
)
from app.repositories.base import BaseRepository

logger = get_logger(__name__)


class FailedMessageRepository(
    BaseRepository[FailedMessageResponse, FailedMessageCreate, FailedMessageUpdate]
):
    """Repository for failed messages."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, "failed_messages", FailedMessageResponse)

    async def create(self, obj_in: FailedMessageCreate) -> FailedMessageResponse:
        """Create a new failed message with proper data transformation."""
        try:
            # Convert Pydantic model to dict, excluding unset fields
            data = obj_in.model_dump(exclude_unset=True)

            # Remove any fields that don't exist in the database table
            # These fields might come from API requests but aren't stored
            invalid_fields = {
                "source_system",
                "destination_system",
                "message_type",
                "error_details",
                "status",
                "created_time",
            }
            for field in invalid_fields:
                data.pop(field, None)

            # Map error_details to error_message if present
            if "error_details" in obj_in.model_dump(exclude_unset=True):
                error_details = obj_in.model_dump(exclude_unset=True)["error_details"]
                if isinstance(error_details, str):
                    data["error_message"] = error_details
                elif isinstance(error_details, dict):
                    data["error_message"] = json.dumps(error_details)

            # Ensure message_body is properly handled as JSON
            if "message_body" in data and data["message_body"] is not None:
                if isinstance(data["message_body"], (dict, list)):
                    # Convert to JSON string for asyncpg
                    from decimal import Decimal

                    def decimal_default(obj):
                        if isinstance(obj, Decimal):
                            return float(obj)
                        raise TypeError

                    data["message_body"] = json.dumps(
                        data["message_body"], default=decimal_default
                    )
                elif isinstance(data["message_body"], str):
                    # String representation, validate it's valid JSON
                    try:
                        # Parse and re-serialize to ensure valid JSON
                        parsed = json.loads(data["message_body"])
                        data["message_body"] = json.dumps(parsed)
                    except json.JSONDecodeError:
                        logger.warning(
                            f"Invalid JSON in message_body: {data['message_body']}"
                        )
                        raise ValidationError("Invalid JSON format in message_body")

            # Build INSERT query
            columns = list(data.keys())
            placeholders = [f"${i + 1}" for i in range(len(columns))]
            values = list(data.values())

            query = f"""
                INSERT INTO {self.table_name} ({", ".join(columns)})
                VALUES ({", ".join(placeholders)})
                RETURNING *
            """

            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, *values)

            if not record:
                raise DatabaseError(f"Failed to create record in {self.table_name}")

            # Instead of trying to parse JSON here, use the get_by_field method
            # which already has the JSON parsing logic working correctly
            created_message_id = record["message_id"]
            return await self.get_by_field("message_id", created_message_id)

        except ValidationError:
            # Re-raise validation errors
            raise
        except asyncpg.PostgresError as e:
            logger.error(f"Database error creating failed message: {e}")
            raise DatabaseError(f"Failed to create failed message: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating failed message: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")

    async def update(self, record_id: int, obj_in):
        """Update a failed message record without adding updated_at timestamp."""
        try:
            # Convert Pydantic model to dict, excluding unset fields
            data = obj_in.model_dump(exclude_unset=True)

            if not data:
                # No fields to update, return current record
                existing = await self.get_by_id(record_id)
                if not existing:
                    raise NotFoundError(self.table_name, str(record_id))
                return existing

            # Don't add updated_at timestamp as the table doesn't have this column

            # Build UPDATE query
            set_clauses = [f"{key} = ${i + 2}" for i, key in enumerate(data.keys())]
            values = [record_id] + list(data.values())

            query = f"""
                UPDATE {self.table_name}
                SET {", ".join(set_clauses)}
                WHERE id = $1
                RETURNING *
            """

            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, *values)

            if not record:
                raise NotFoundError(self.table_name, str(record_id))

            # Parse JSON fields in the returned record
            record_dict = dict(record)

            # Parse message_body JSON field
            if (
                "message_body" in record_dict
                and record_dict["message_body"] is not None
            ):
                if isinstance(record_dict["message_body"], str):
                    try:
                        record_dict["message_body"] = json.loads(
                            record_dict["message_body"]
                        )
                    except json.JSONDecodeError:
                        logger.warning(
                            f"Failed to parse JSON field message_body: {record_dict['message_body']}"
                        )
                        record_dict["message_body"] = None

            return self.model_class(**record_dict)

        except asyncpg.PostgresError as e:
            logger.error(f"Database error updating record in {self.table_name}: {e}")
            raise DatabaseError(f"Failed to update record: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error updating record in {self.table_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")

    async def list_with_filters(
        self, pagination: PaginationParams, filters: FailedMessageFilterParams
    ) -> PaginatedResponse:
        """List failed messages with advanced filtering."""
        try:
            # Build WHERE conditions
            where_conditions = []
            where_values = []
            param_count = 0

            # Add filter conditions
            if filters.queue_name:
                param_count += 1
                where_conditions.append(f"queue_name = ${param_count}")
                where_values.append(filters.queue_name)

            if filters.event_type:
                param_count += 1
                where_conditions.append(f"event_type = ${param_count}")
                where_values.append(filters.event_type)

            if filters.resolved is not None:
                param_count += 1
                where_conditions.append(f"resolved = ${param_count}")
                where_values.append(filters.resolved)

            if filters.min_retry_count is not None:
                param_count += 1
                where_conditions.append(f"retry_count >= ${param_count}")
                where_values.append(filters.min_retry_count)

            if filters.max_retry_count is not None:
                param_count += 1
                where_conditions.append(f"retry_count <= ${param_count}")
                where_values.append(filters.max_retry_count)

            if filters.start_date:
                param_count += 1
                where_conditions.append(f"first_failed_at >= ${param_count}")
                where_values.append(filters.start_date)

            if filters.end_date:
                param_count += 1
                where_conditions.append(f"first_failed_at <= ${param_count}")
                where_values.append(filters.end_date)

            if filters.search:
                param_count += 1
                where_conditions.append(
                    f"(queue_name ILIKE ${param_count} OR event_type ILIKE ${param_count})"
                )
                search_term = f"%{filters.search}%"
                where_values.extend([search_term, search_term])
                param_count += 1  # Account for the second parameter

            where_clause = (
                f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            )

            # Build ORDER BY clause
            order_by = filters.sort_by or "first_failed_at"
            order_direction = "DESC" if filters.sort_order == "desc" else "ASC"
            order_clause = f"ORDER BY {order_by} {order_direction}"

            # Count total records
            count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_clause}"

            # Get paginated records
            offset = pagination.offset
            limit = pagination.size
            param_count += 1
            limit_param = param_count
            param_count += 1
            offset_param = param_count

            list_query = f"""
                SELECT * FROM {self.table_name}
                {where_clause}
                {order_clause}
                LIMIT ${limit_param} OFFSET ${offset_param}
            """

            async with self.db_manager.get_connection() as connection:
                # Get total count
                total = await connection.fetchval(count_query, *where_values)

                # Get records
                records = await connection.fetch(
                    list_query, *where_values, limit, offset
                )

            # Convert records to models
            items = [
                self.model_class.model_validate(dict(record)) for record in records
            ]

            return PaginatedResponse.create(
                items=items,
                total=total,
                page=pagination.page,
                size=pagination.size,
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error listing failed messages with filters: {e}")
            raise DatabaseError(f"Failed to list failed messages: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error listing failed messages with filters: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")

    async def get_failed_message_statistics(
        self, start_date=None, end_date=None, queue_name=None
    ):
        """Get failed message statistics."""
        try:
            # Build WHERE conditions for date filtering
            where_conditions = []
            where_values = []
            param_count = 0

            if start_date:
                param_count += 1
                where_conditions.append(f"first_failed_at >= ${param_count}")
                where_values.append(start_date)

            if end_date:
                param_count += 1
                where_conditions.append(f"first_failed_at <= ${param_count}")
                where_values.append(end_date)

            if queue_name:
                param_count += 1
                where_conditions.append(f"queue_name = ${param_count}")
                where_values.append(queue_name)

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # Get basic statistics
            stats_query = f"""
                SELECT
                    COUNT(*) as total_failed_messages,
                    COUNT(CASE WHEN resolved_at IS NULL THEN 1 END) as unresolved_messages,
                    COUNT(CASE WHEN resolved_at IS NOT NULL THEN 1 END) as resolved_messages,
                    AVG(retry_count) as average_retry_count,
                    CASE
                        WHEN COUNT(*) > 0 THEN
                            COUNT(CASE WHEN resolved_at IS NOT NULL THEN 1 END) * 100.0 / COUNT(*)
                        ELSE 0
                    END as resolution_rate
                FROM {self.table_name}
                {where_clause}
            """

            # Get queue breakdown
            queue_query = f"""
                SELECT queue_name, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY queue_name
            """

            # Get event type breakdown
            event_type_query = f"""
                SELECT event_type, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY event_type
            """

            async with self.db_manager.get_connection() as connection:
                # Get basic stats
                stats_record = await connection.fetchrow(stats_query, *where_values)

                # Get queue breakdown
                queue_records = await connection.fetch(queue_query, *where_values)

                # Get event type breakdown
                event_type_records = await connection.fetch(
                    event_type_query, *where_values
                )

            # Build response
            by_queue = {
                record["queue_name"]: record["count"] for record in queue_records
            }
            by_event_type = {
                record["event_type"] or "unknown": record["count"]
                for record in event_type_records
            }

            from app.models.failed_messages import FailedMessageStatsResponse

            return FailedMessageStatsResponse(
                total_failed_messages=stats_record["total_failed_messages"],
                unresolved_messages=stats_record["unresolved_messages"],
                resolved_messages=stats_record["resolved_messages"],
                average_retry_count=float(stats_record["average_retry_count"] or 0),
                by_queue=by_queue,
                by_event_type=by_event_type,
                recent_failures=[],  # Could be implemented later
                resolution_rate=float(stats_record["resolution_rate"] or 0),
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting failed message statistics: {e}")
            raise DatabaseError(f"Failed to get failed message statistics: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting failed message statistics: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")

    async def get_by_field(self, field_name: str, field_value):
        """Get a failed message by a specific field, with JSON field parsing."""
        try:
            query = f"SELECT * FROM {self.table_name} WHERE {field_name} = $1"

            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, field_value)

                if record:
                    # Parse JSON fields
                    record_dict = dict(record)

                    # Parse message_body JSON field
                    if (
                        "message_body" in record_dict
                        and record_dict["message_body"] is not None
                    ):
                        if isinstance(record_dict["message_body"], str):
                            try:
                                record_dict["message_body"] = json.loads(
                                    record_dict["message_body"]
                                )
                            except json.JSONDecodeError:
                                logger.warning(
                                    f"Failed to parse JSON field message_body: {record_dict['message_body']}"
                                )
                                record_dict["message_body"] = None

                    return self.model_class(**record_dict)
                return None

        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting failed message by {field_name}: {e}")
            raise DatabaseError(f"Failed to get failed message: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error getting failed message by {field_name}: {e}"
            )
            raise DatabaseError(f"Unexpected error: {str(e)}")
