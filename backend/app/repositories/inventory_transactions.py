"""
Repository for inventory transactions.
"""

import asyncpg

from app.core.database import DatabaseManager
from app.core.exceptions import DatabaseError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.inventory_transactions import (
    InventoryTransactionCreate,
    InventoryTransactionFilterParams,
    InventoryTransactionResponse,
    InventoryTransactionUpdate,
)
from app.repositories.base import BaseRepository

logger = get_logger(__name__)


class InventoryTransactionRepository(
    BaseRepository[
        InventoryTransactionResponse,
        InventoryTransactionCreate,
        InventoryTransactionUpdate,
    ]
):
    """Repository for inventory transactions."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(
            db_manager, "inventory_transactions", InventoryTransactionResponse
        )

    def _parse_record(self, record):
        """Parse database record and handle JSON fields."""
        import json

        record_dict = dict(record)

        # Parse JSON fields that are stored as strings
        json_fields = ["storage_conditions", "cycle_count_info"]
        for field in json_fields:
            if field in record_dict and isinstance(record_dict[field], str):
                try:
                    parsed_data = json.loads(record_dict[field])

                    # Fix storage_conditions field names
                    if field == "storage_conditions" and isinstance(parsed_data, dict):
                        # Map incorrect field names to correct ones
                        corrected_data = {}
                        for key, value in parsed_data.items():
                            if key == "humidity_percent":
                                corrected_data["humidity"] = value
                            elif key == "temperature_celsius":
                                corrected_data["temperature"] = value
                            elif key in [
                                "special_requirements",
                                "temperature",
                                "humidity",
                            ]:
                                corrected_data[key] = value
                        record_dict[field] = corrected_data
                    else:
                        record_dict[field] = parsed_data
                except (json.JSONDecodeError, TypeError):
                    record_dict[field] = {}

        # Handle enum fields that might have invalid values
        if "quality_status" in record_dict and record_dict["quality_status"] not in [
            "good",
            "damaged",
            "expired",
            "quarantine",
        ]:
            record_dict["quality_status"] = (
                "good"  # Default to 'good' for invalid values
            )

        return record_dict

    async def list_with_filters(
        self, pagination: PaginationParams, filters: InventoryTransactionFilterParams
    ) -> PaginatedResponse:
        """List inventory transactions with advanced filtering."""
        try:
            # Build WHERE conditions
            where_conditions = []
            where_values = []
            param_count = 0

            # Add filter conditions
            if filters.item_sku:
                param_count += 1
                where_conditions.append(f"item_sku = ${param_count}")
                where_values.append(filters.item_sku)

            if filters.transaction_type:
                param_count += 1
                where_conditions.append(f"transaction_type = ${param_count}")
                where_values.append(filters.transaction_type.value)

            if filters.operator_id:
                param_count += 1
                where_conditions.append(f"operator_id = ${param_count}")
                where_values.append(filters.operator_id)

            if filters.location:
                param_count += 1
                where_conditions.append(f"location = ${param_count}")
                where_values.append(filters.location)

            if filters.zone:
                param_count += 1
                where_conditions.append(f"zone = ${param_count}")
                where_values.append(filters.zone)

            if filters.lot_number:
                param_count += 1
                where_conditions.append(f"lot_number = ${param_count}")
                where_values.append(filters.lot_number)

            if filters.quality_status:
                param_count += 1
                where_conditions.append(f"quality_status = ${param_count}")
                where_values.append(filters.quality_status.value)

            if filters.start_date:
                param_count += 1
                where_conditions.append(f"transaction_time >= ${param_count}")
                where_values.append(filters.start_date)

            if filters.end_date:
                param_count += 1
                where_conditions.append(f"transaction_time <= ${param_count}")
                where_values.append(filters.end_date)

            if filters.search:
                param_count += 1
                where_conditions.append(
                    f"(item_sku ILIKE ${param_count} OR location ILIKE ${param_count})"
                )
                search_term = f"%{filters.search}%"
                where_values.extend([search_term, search_term])
                param_count += 1  # Account for the second parameter

            where_clause = (
                f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            )

            # Build ORDER BY clause
            order_by = filters.sort_by or "transaction_time"
            order_direction = "DESC" if filters.sort_order == "desc" else "ASC"
            order_clause = f"ORDER BY {order_by} {order_direction}"

            # Count total records
            count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_clause}"

            # Get paginated records
            offset = pagination.offset
            limit = pagination.size
            param_count += 1
            limit_param = param_count
            param_count += 1
            offset_param = param_count

            list_query = f"""
                SELECT * FROM {self.table_name}
                {where_clause}
                {order_clause}
                LIMIT ${limit_param} OFFSET ${offset_param}
            """

            async with self.db_manager.get_connection() as connection:
                # Get total count
                total = await connection.fetchval(count_query, *where_values)

                # Get records
                records = await connection.fetch(
                    list_query, *where_values, limit, offset
                )

            # Convert records to models
            items = [
                self.model_class.model_validate(self._parse_record(record))
                for record in records
            ]

            return PaginatedResponse.create(
                items=items,
                total=total,
                page=pagination.page,
                size=pagination.size,
            )

        except asyncpg.PostgresError as e:
            logger.error(
                f"Database error listing inventory transactions with filters: {e}"
            )
            raise DatabaseError(f"Failed to list inventory transactions: {str(e)}")
        except Exception as e:
            logger.error(
                f"Unexpected error listing inventory transactions with filters: {e}"
            )
            raise DatabaseError(f"Unexpected error: {str(e)}")

    async def get_inventory_statistics(
        self, start_date=None, end_date=None, location=None
    ):
        """Get inventory statistics."""
        try:
            # Build WHERE conditions for filtering
            where_conditions = []
            where_values = []
            param_count = 0

            if start_date:
                param_count += 1
                where_conditions.append(f"transaction_time >= ${param_count}")
                where_values.append(start_date)

            if end_date:
                param_count += 1
                where_conditions.append(f"transaction_time <= ${param_count}")
                where_values.append(end_date)

            if location:
                param_count += 1
                where_conditions.append(f"location = ${param_count}")
                where_values.append(location)

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # Get basic statistics
            stats_query = f"""
                SELECT
                    COUNT(*) as total_transactions,
                    SUM(quantity_change * cost_per_unit) as total_value
                FROM {self.table_name}
                {where_clause}
            """

            # Get transaction type breakdown
            type_query = f"""
                SELECT transaction_type, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY transaction_type
            """

            # Get location breakdown
            location_query = f"""
                SELECT location, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY location
            """

            # Get quality status breakdown
            quality_query = f"""
                SELECT quality_status, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY quality_status
            """

            # Get top items by transaction volume
            top_items_query = f"""
                SELECT item_sku, item_description, COUNT(*) as transaction_count
                FROM {self.table_name}
                {where_clause}
                GROUP BY item_sku, item_description
                ORDER BY transaction_count DESC
                LIMIT 10
            """

            async with self.db_manager.get_connection() as connection:
                # Get basic stats
                stats_result = await connection.fetchrow(stats_query, *where_values)

                # Get breakdowns
                type_results = await connection.fetch(type_query, *where_values)
                location_results = await connection.fetch(location_query, *where_values)
                quality_results = await connection.fetch(quality_query, *where_values)
                top_items_results = await connection.fetch(
                    top_items_query, *where_values
                )

            # Build response
            from decimal import Decimal

            from app.models.inventory_transactions import InventoryStatsResponse

            return InventoryStatsResponse(
                total_transactions=stats_result["total_transactions"] or 0,
                total_value=Decimal(str(stats_result["total_value"] or 0)),
                by_transaction_type={
                    str(row["transaction_type"]): row["count"] for row in type_results
                },
                by_location={
                    str(row["location"]): row["count"] for row in location_results
                },
                by_quality_status={
                    str(row["quality_status"]): row["count"] for row in quality_results
                },
                top_items=[
                    {
                        "item_sku": row["item_sku"],
                        "item_description": row["item_description"],
                        "transaction_count": row["transaction_count"],
                    }
                    for row in top_items_results
                ],
                inventory_turnover=None,  # Can be calculated later with more complex logic
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting inventory statistics: {e}")
            raise DatabaseError(f"Failed to get inventory statistics: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting inventory statistics: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
