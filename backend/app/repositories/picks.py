"""
Repository for pick operations.

This module provides database operations specific to pick entities,
including advanced filtering and pick-specific business logic.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

import asyncpg

from app.core.database import DatabaseManager
from app.core.exceptions import DatabaseError
from app.models.picks import (
    Pick<PERSON><PERSON>ponse, PickCreate, PickUpdate, PickFilterParams, PickStatsResponse
)
from app.models.base import PaginationParams, PaginatedResponse
from app.repositories.base import BaseRepository

logger = logging.getLogger(__name__)


class PickRepository(BaseRepository[PickResponse, PickCreate, PickUpdate]):
    """Repository for pick operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, "picks", PickResponse)
    
    async def get_by_pick_id(self, pick_id: str) -> Optional[PickResponse]:
        """Get a pick by pick_id."""
        return await self.get_by_field("pick_id", pick_id)
    
    async def list_with_filters(
        self,
        pagination: PaginationParams,
        filters: PickFilterParams
    ) -> PaginatedResponse:
        """List picks with advanced filtering."""
        try:
            # Build WHERE conditions
            where_conditions = []
            where_values = []
            param_count = 0
            
            # Add filter conditions
            if filters.operator_id:
                param_count += 1
                where_conditions.append(f"operator_id = ${param_count}")
                where_values.append(filters.operator_id)
            
            if filters.item_sku:
                param_count += 1
                where_conditions.append(f"item_sku = ${param_count}")
                where_values.append(filters.item_sku)
            
            if filters.pick_status:
                param_count += 1
                where_conditions.append(f"pick_status = ${param_count}")
                where_values.append(filters.pick_status.value)
            
            if filters.priority:
                param_count += 1
                where_conditions.append(f"priority = ${param_count}")
                where_values.append(filters.priority.value)
            
            if filters.zone:
                param_count += 1
                where_conditions.append(f"zone = ${param_count}")
                where_values.append(filters.zone)
            
            if filters.batch_id:
                param_count += 1
                where_conditions.append(f"batch_id = ${param_count}")
                where_values.append(filters.batch_id)
            
            if filters.start_date:
                param_count += 1
                where_conditions.append(f"start_time >= ${param_count}")
                where_values.append(filters.start_date)
            
            if filters.end_date:
                param_count += 1
                where_conditions.append(f"start_time <= ${param_count}")
                where_values.append(filters.end_date)
            
            if filters.search:
                param_count += 1
                where_conditions.append(f"(pick_id ILIKE ${param_count} OR item_description ILIKE ${param_count})")
                search_term = f"%{filters.search}%"
                where_values.extend([search_term, search_term])
                param_count += 1  # Account for the second parameter
            
            where_clause = f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            
            # Build ORDER BY clause
            order_by = filters.sort_by or "start_time"
            order_direction = "DESC" if filters.sort_order == "desc" else "ASC"
            order_clause = f"ORDER BY {order_by} {order_direction}"
            
            # Count total records
            count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_clause}"
            
            # Get paginated records
            offset = pagination.offset
            limit = pagination.size
            param_count += 1
            limit_param = param_count
            param_count += 1
            offset_param = param_count
            
            list_query = f"""
                SELECT * FROM {self.table_name}
                {where_clause}
                {order_clause}
                LIMIT ${limit_param} OFFSET ${offset_param}
            """
            
            async with self.db_manager.get_connection() as connection:
                # Get total count
                total = await connection.fetchval(count_query, *where_values)
                
                # Get records
                records = await connection.fetch(list_query, *where_values, limit, offset)
            
            # Convert records to models
            items = [self.model_class.model_validate(dict(record)) for record in records]
            
            return PaginatedResponse.create(
                items=items,
                total=total,
                page=pagination.page,
                size=pagination.size,
            )
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error listing picks with filters: {e}")
            raise DatabaseError(f"Failed to list picks: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error listing picks with filters: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def get_picks_by_operator(self, operator_id: str, limit: int = 100) -> List[PickResponse]:
        """Get picks for a specific operator."""
        try:
            query = f"""
                SELECT * FROM {self.table_name}
                WHERE operator_id = $1
                ORDER BY start_time DESC
                LIMIT $2
            """
            
            async with self.db_manager.get_connection() as connection:
                records = await connection.fetch(query, operator_id, limit)
            
            return [self.model_class.model_validate(dict(record)) for record in records]
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting picks by operator: {e}")
            raise DatabaseError(f"Failed to get picks by operator: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting picks by operator: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def get_picks_by_batch(self, batch_id: str) -> List[PickResponse]:
        """Get all picks in a batch."""
        try:
            query = f"""
                SELECT * FROM {self.table_name}
                WHERE batch_id = $1
                ORDER BY start_time ASC
            """
            
            async with self.db_manager.get_connection() as connection:
                records = await connection.fetch(query, batch_id)
            
            return [self.model_class.model_validate(dict(record)) for record in records]
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting picks by batch: {e}")
            raise DatabaseError(f"Failed to get picks by batch: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting picks by batch: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def get_pick_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        operator_id: Optional[str] = None
    ) -> PickStatsResponse:
        """Get pick statistics."""
        try:
            # Build WHERE conditions
            where_conditions = []
            where_values = []
            param_count = 0
            
            if start_date:
                param_count += 1
                where_conditions.append(f"start_time >= ${param_count}")
                where_values.append(start_date)
            
            if end_date:
                param_count += 1
                where_conditions.append(f"start_time <= ${param_count}")
                where_values.append(end_date)
            
            if operator_id:
                param_count += 1
                where_conditions.append(f"operator_id = ${param_count}")
                where_values.append(operator_id)
            
            where_clause = f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            
            query = f"""
                SELECT 
                    COUNT(*) as total_picks,
                    COUNT(*) FILTER (WHERE pick_status = 'completed') as completed_picks,
                    COUNT(*) FILTER (WHERE pick_status = 'pending') as pending_picks,
                    COUNT(*) FILTER (WHERE pick_status = 'in_progress') as in_progress_picks,
                    COUNT(*) FILTER (WHERE pick_status = 'short_picked') as short_picked,
                    COUNT(*) FILTER (WHERE pick_status = 'cancelled') as cancelled_picks,
                    AVG(duration_seconds) FILTER (WHERE duration_seconds IS NOT NULL) as average_duration,
                    SUM(quantity_picked) as total_items_picked,
                    AVG(CASE WHEN quantity_requested > 0 THEN 
                        CAST(quantity_picked AS FLOAT) / quantity_requested 
                        ELSE NULL END) as pick_accuracy
                FROM {self.table_name}
                {where_clause}
            """
            
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, *where_values)
            
            return PickStatsResponse(
                total_picks=record["total_picks"] or 0,
                completed_picks=record["completed_picks"] or 0,
                pending_picks=record["pending_picks"] or 0,
                in_progress_picks=record["in_progress_picks"] or 0,
                short_picked=record["short_picked"] or 0,
                cancelled_picks=record["cancelled_picks"] or 0,
                average_duration=float(record["average_duration"]) if record["average_duration"] else None,
                total_items_picked=record["total_items_picked"] or 0,
                pick_accuracy=float(record["pick_accuracy"]) * 100 if record["pick_accuracy"] else None,
            )
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting pick statistics: {e}")
            raise DatabaseError(f"Failed to get pick statistics: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting pick statistics: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
    
    async def update_pick_status(self, pick_id: str, status: str, end_time: Optional[datetime] = None) -> PickResponse:
        """Update pick status and optionally set end time."""
        try:
            # Get current pick
            current_pick = await self.get_by_pick_id(pick_id)
            if not current_pick:
                raise DatabaseError(f"Pick {pick_id} not found")
            
            # Prepare update data
            update_data = {"pick_status": status, "updated_at": datetime.utcnow()}
            
            if end_time:
                update_data["end_time"] = end_time
                # Calculate duration if start_time exists
                if current_pick.start_time:
                    duration = int((end_time - current_pick.start_time).total_seconds())
                    update_data["duration_seconds"] = duration
            
            # Build UPDATE query
            set_clauses = [f"{key} = ${i+2}" for i, key in enumerate(update_data.keys())]
            values = [pick_id] + list(update_data.values())
            
            query = f"""
                UPDATE {self.table_name}
                SET {', '.join(set_clauses)}
                WHERE pick_id = $1
                RETURNING *
            """
            
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, *values)
            
            if not record:
                raise DatabaseError(f"Failed to update pick {pick_id}")
            
            return self.model_class.model_validate(dict(record))
            
        except asyncpg.PostgresError as e:
            logger.error(f"Database error updating pick status: {e}")
            raise DatabaseError(f"Failed to update pick status: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error updating pick status: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
