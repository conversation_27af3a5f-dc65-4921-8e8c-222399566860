"""
Repository for shipments.
"""

import asyncpg

from app.core.database import DatabaseManager
from app.core.exceptions import DatabaseError, ValidationError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.shipments import (
    ShipmentCreate,
    ShipmentFilterParams,
    ShipmentResponse,
    ShipmentUpdate,
)
from app.repositories.base import BaseRepository

logger = get_logger(__name__)


class ShipmentRepository(
    BaseRepository[ShipmentResponse, ShipmentCreate, ShipmentUpdate]
):
    """Repository for shipments."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, "shipments", ShipmentResponse)

    async def create(self, obj_in: ShipmentCreate) -> ShipmentResponse:
        """Create a new shipment with proper data transformation."""
        try:
            # Convert Pydantic model to dict, excluding unset fields
            data = obj_in.model_dump(exclude_unset=True)

            # Remove any fields that don't exist in the database table
            # These fields might come from API requests but aren't stored
            invalid_fields = {"status", "priority"}
            for field in invalid_fields:
                data.pop(field, None)

            # Add default values for required fields
            if "shipment_status" not in data:
                data["shipment_status"] = "pending"

            # Ensure JSON fields are properly serialized
            json_fields = [
                "packages",
                "shipping_address",
                "billing_address",
                "hazmat_info",
            ]
            for field in json_fields:
                if field in data and data[field] is not None:
                    if isinstance(data[field], (dict, list)):
                        # Convert to JSON string for asyncpg
                        import json
                        from decimal import Decimal

                        def decimal_default(obj):
                            if isinstance(obj, Decimal):
                                return float(obj)
                            raise TypeError

                        data[field] = json.dumps(data[field], default=decimal_default)
                    elif isinstance(data[field], str):
                        # String representation, validate it's valid JSON
                        import json

                        try:
                            # Parse and re-serialize to ensure valid JSON
                            parsed = json.loads(data[field])
                            data[field] = json.dumps(parsed)
                        except json.JSONDecodeError:
                            logger.warning(
                                f"Invalid JSON in field {field}: {data[field]}"
                            )
                            raise ValidationError(f"Invalid JSON format in {field}")

            # Build INSERT query
            columns = list(data.keys())
            placeholders = [f"${i + 1}" for i in range(len(columns))]
            values = list(data.values())

            query = f"""
                INSERT INTO {self.table_name} ({", ".join(columns)})
                VALUES ({", ".join(placeholders)})
                RETURNING *
            """

            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, *values)

            if not record:
                raise DatabaseError(f"Failed to create record in {self.table_name}")

            # Parse the record and return as model
            parsed_record = self._parse_record(record)
            return self.model_class.model_validate(parsed_record)

        except ValidationError:
            # Re-raise validation errors
            raise
        except asyncpg.PostgresError as e:
            logger.error(f"Database error creating shipment: {e}")
            raise DatabaseError(f"Failed to create shipment: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error creating shipment: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")

    async def get_by_field(self, field_name: str, field_value):
        """Get a shipment by a specific field, with JSON field parsing."""
        try:
            query = f"SELECT * FROM {self.table_name} WHERE {field_name} = $1"

            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, field_value)

                if record:
                    # Parse JSON fields
                    record_dict = dict(record)
                    json_fields = [
                        "packages",
                        "shipping_address",
                        "billing_address",
                        "hazmat_info",
                    ]

                    import json

                    for field in json_fields:
                        if field in record_dict and record_dict[field] is not None:
                            if isinstance(record_dict[field], str):
                                try:
                                    record_dict[field] = json.loads(record_dict[field])
                                except json.JSONDecodeError:
                                    logger.warning(
                                        f"Failed to parse JSON field {field}: {record_dict[field]}"
                                    )
                                    record_dict[field] = None

                    return self.model_class(**record_dict)
                return None

        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting shipment by {field_name}: {e}")
            raise DatabaseError(f"Failed to get shipment: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting shipment by {field_name}: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")

    def _parse_record(self, record):
        """Parse database record and handle JSON fields."""
        import json

        record_dict = dict(record)

        # Parse JSON fields that are stored as strings
        json_fields = ["packages", "shipping_address", "billing_address", "hazmat_info"]
        for field in json_fields:
            if field in record_dict and isinstance(record_dict[field], str):
                try:
                    parsed_data = json.loads(record_dict[field])

                    # Fix packages structure - ensure dimensions field exists
                    if field == "packages" and isinstance(parsed_data, list):
                        for package in parsed_data:
                            if (
                                isinstance(package, dict)
                                and "dimensions" not in package
                            ):
                                # Create dimensions from individual fields if they exist
                                dimensions = {}
                                for dim_field in ["width", "height", "length"]:
                                    if dim_field in package:
                                        dimensions[dim_field] = package.pop(dim_field)
                                if dimensions:
                                    package["dimensions"] = dimensions
                                else:
                                    # Set default dimensions if none exist
                                    package["dimensions"] = {
                                        "width": 10.0,
                                        "height": 10.0,
                                        "length": 10.0,
                                    }

                    # Fix address structure - ensure street field exists and truncate long fields
                    elif field in [
                        "shipping_address",
                        "billing_address",
                    ] and isinstance(parsed_data, dict):
                        if "street" not in parsed_data:
                            # Use address_line1 as street if available
                            if "address_line1" in parsed_data:
                                parsed_data["street"] = parsed_data["address_line1"]
                            else:
                                parsed_data["street"] = "Unknown Street"

                        # Truncate state and country to 3 characters max
                        if (
                            "state" in parsed_data
                            and len(str(parsed_data["state"])) > 3
                        ):
                            parsed_data["state"] = str(parsed_data["state"])[:3]
                        if (
                            "country" in parsed_data
                            and len(str(parsed_data["country"])) > 3
                        ):
                            parsed_data["country"] = str(parsed_data["country"])[:3]

                    record_dict[field] = parsed_data
                except (json.JSONDecodeError, TypeError):
                    if field == "packages":
                        record_dict[field] = []
                    else:
                        record_dict[field] = {}

        # Handle enum fields that might have invalid values
        if "shipment_status" in record_dict and record_dict["shipment_status"] not in [
            "pending",
            "processing",
            "packed",
            "shipped",
            "in_transit",
            "delivered",
            "cancelled",
        ]:
            record_dict["shipment_status"] = (
                "pending"  # Default to 'pending' for invalid values
            )

        if "service_level" in record_dict and record_dict["service_level"] not in [
            "ground",
            "air",
            "express",
            "overnight",
        ]:
            record_dict["service_level"] = (
                "ground"  # Default to 'ground' for invalid values
            )

        # Handle shipped_time validation - ensure it's after created_time
        if (
            "shipped_time" in record_dict
            and "created_time" in record_dict
            and record_dict["shipped_time"] is not None
            and record_dict["created_time"] is not None
        ):
            if record_dict["shipped_time"] <= record_dict["created_time"]:
                # Set shipped_time to None to avoid validation error
                record_dict["shipped_time"] = None

        return record_dict

    async def list_with_filters(
        self, pagination: PaginationParams, filters: ShipmentFilterParams
    ) -> PaginatedResponse:
        """List shipments with advanced filtering."""
        try:
            # Build WHERE conditions
            where_conditions = []
            where_values = []
            param_count = 0

            # Add filter conditions
            if filters.order_id:
                param_count += 1
                where_conditions.append(f"order_id = ${param_count}")
                where_values.append(filters.order_id)

            if filters.customer_id:
                param_count += 1
                where_conditions.append(f"customer_id = ${param_count}")
                where_values.append(filters.customer_id)

            if filters.shipment_type:
                param_count += 1
                where_conditions.append(f"shipment_type = ${param_count}")
                where_values.append(filters.shipment_type.value)

            if filters.shipment_status:
                param_count += 1
                where_conditions.append(f"shipment_status = ${param_count}")
                where_values.append(filters.shipment_status.value)

            if filters.carrier:
                param_count += 1
                where_conditions.append(f"carrier = ${param_count}")
                where_values.append(filters.carrier)

            if filters.service_level:
                param_count += 1
                where_conditions.append(f"service_level = ${param_count}")
                where_values.append(filters.service_level.value)

            if filters.operator_id:
                param_count += 1
                where_conditions.append(f"operator_id = ${param_count}")
                where_values.append(filters.operator_id)

            if filters.dock_door:
                param_count += 1
                where_conditions.append(f"dock_door = ${param_count}")
                where_values.append(filters.dock_door)

            if filters.start_date:
                param_count += 1
                where_conditions.append(f"ship_date >= ${param_count}")
                where_values.append(filters.start_date)

            if filters.end_date:
                param_count += 1
                where_conditions.append(f"ship_date <= ${param_count}")
                where_values.append(filters.end_date)

            if filters.min_weight is not None:
                param_count += 1
                where_conditions.append(f"total_weight >= ${param_count}")
                where_values.append(filters.min_weight)

            if filters.max_weight is not None:
                param_count += 1
                where_conditions.append(f"total_weight <= ${param_count}")
                where_values.append(filters.max_weight)

            if filters.search:
                param_count += 1
                where_conditions.append(
                    f"(order_id ILIKE ${param_count} OR tracking_number ILIKE ${param_count})"
                )
                search_term = f"%{filters.search}%"
                where_values.extend([search_term, search_term])
                param_count += 1  # Account for the second parameter

            where_clause = (
                f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
            )

            # Build ORDER BY clause
            order_by = filters.sort_by or "ship_date"
            order_direction = "DESC" if filters.sort_order == "desc" else "ASC"
            order_clause = f"ORDER BY {order_by} {order_direction}"

            # Count total records
            count_query = f"SELECT COUNT(*) FROM {self.table_name} {where_clause}"

            # Get paginated records
            offset = pagination.offset
            limit = pagination.size
            param_count += 1
            limit_param = param_count
            param_count += 1
            offset_param = param_count

            list_query = f"""
                SELECT * FROM {self.table_name}
                {where_clause}
                {order_clause}
                LIMIT ${limit_param} OFFSET ${offset_param}
            """

            async with self.db_manager.get_connection() as connection:
                # Get total count
                total = await connection.fetchval(count_query, *where_values)

                # Get records
                records = await connection.fetch(
                    list_query, *where_values, limit, offset
                )

            # Convert records to models
            items = [
                self.model_class.model_validate(self._parse_record(record))
                for record in records
            ]

            return PaginatedResponse.create(
                items=items,
                total=total,
                page=pagination.page,
                size=pagination.size,
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error listing shipments with filters: {e}")
            raise DatabaseError(f"Failed to list shipments: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error listing shipments with filters: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")

    async def get_shipment_statistics(
        self, start_date=None, end_date=None, carrier=None
    ):
        """Get shipment statistics."""
        try:
            # Build WHERE conditions for filtering
            where_conditions = []
            where_values = []
            param_count = 0

            if start_date:
                param_count += 1
                where_conditions.append(f"ship_date >= ${param_count}")
                where_values.append(start_date)

            if end_date:
                param_count += 1
                where_conditions.append(f"ship_date <= ${param_count}")
                where_values.append(end_date)

            if carrier:
                param_count += 1
                where_conditions.append(f"carrier = ${param_count}")
                where_values.append(carrier)

            # Add actual_delivery filter to where conditions
            where_conditions.append("actual_delivery IS NOT NULL")

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # Get basic statistics
            stats_query = f"""
                SELECT
                    COUNT(*) as total_shipments,
                    SUM(total_weight_kg) as total_weight,
                    SUM(shipping_cost) as total_cost,
                    AVG(EXTRACT(EPOCH FROM (actual_delivery - shipped_time))/3600) as average_delivery_time,
                    COUNT(CASE WHEN actual_delivery <= estimated_delivery THEN 1 END) * 100.0 / COUNT(*) as on_time_delivery_rate
                FROM {self.table_name}
                {where_clause}
            """

            # Get status breakdown
            status_query = f"""
                SELECT shipment_status, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY shipment_status
            """

            # Get carrier breakdown
            carrier_query = f"""
                SELECT carrier, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY carrier
            """

            # Get shipment type breakdown
            type_query = f"""
                SELECT shipment_type, COUNT(*) as count
                FROM {self.table_name}
                {where_clause}
                GROUP BY shipment_type
            """

            async with self.db_manager.get_connection() as connection:
                # Get basic stats
                stats_result = await connection.fetchrow(stats_query, *where_values)

                # Get breakdowns
                status_results = await connection.fetch(status_query, *where_values)
                carrier_results = await connection.fetch(carrier_query, *where_values)
                type_results = await connection.fetch(type_query, *where_values)

            # Build response
            from decimal import Decimal

            from app.models.shipments import ShipmentStatsResponse

            return ShipmentStatsResponse(
                total_shipments=stats_result["total_shipments"] or 0,
                total_weight=Decimal(str(stats_result["total_weight"] or 0)),
                total_cost=Decimal(str(stats_result["total_cost"] or 0)),
                average_delivery_time=Decimal(
                    str(stats_result["average_delivery_time"] or 0)
                )
                if stats_result["average_delivery_time"]
                else None,
                on_time_delivery_rate=Decimal(
                    str(stats_result["on_time_delivery_rate"] or 0)
                )
                if stats_result["on_time_delivery_rate"]
                else None,
                by_status={
                    str(row["shipment_status"]): row["count"] for row in status_results
                },
                by_carrier={
                    str(row["carrier"]): row["count"] for row in carrier_results
                },
                by_shipment_type={
                    str(row["shipment_type"]): row["count"] for row in type_results
                },
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error getting shipment statistics: {e}")
            raise DatabaseError(f"Failed to get shipment statistics: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error getting shipment statistics: {e}")
            raise DatabaseError(f"Unexpected error: {str(e)}")
