"""
User repository for database operations.

This module provides database operations for user management including
CRUD operations, authentication queries, and user-related database interactions.
"""

import logging
from datetime import datetime
from typing import Optional

import asyncpg

from app.core.database import DatabaseManager
from app.models.users import UserCreate, UserInDB, UserUpdate
from app.repositories.base import BaseRepository

logger = logging.getLogger(__name__)


class UserRepository(BaseRepository[UserInDB, UserCreate, UserUpdate]):
    """Repository for user database operations."""

    def __init__(self, db_manager: DatabaseManager):
        super().__init__(db_manager, "users", UserInDB)

    async def create_user(
        self, user_data: UserCreate, hashed_password: str
    ) -> UserInDB:
        """Create a new user in the database."""
        query = """
            INSERT INTO users (email, full_name, hashed_password, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id, email, full_name, hashed_password, is_active, created_at, updated_at
        """

        now = datetime.utcnow()

        try:
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(
                    query,
                    user_data.email,
                    user_data.full_name,
                    hashed_password,
                    user_data.is_active,
                    now,
                    now,
                )

                if record:
                    return UserInDB(**dict(record))
                else:
                    raise ValueError("Failed to create user")

        except asyncpg.UniqueViolationError:
            logger.warning(
                f"Attempt to create user with existing email: {user_data.email}"
            )
            raise ValueError("User with this email already exists")
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise

    async def get_user_by_email(self, email: str) -> Optional[UserInDB]:
        """Get a user by email address."""
        query = """
            SELECT id, email, full_name, hashed_password, is_active, created_at, updated_at
            FROM users
            WHERE email = $1
        """

        try:
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, email)

                if record:
                    return UserInDB(**dict(record))
                return None

        except Exception as e:
            logger.error(f"Error getting user by email {email}: {e}")
            raise

    async def get_user_by_id(self, user_id: int) -> Optional[UserInDB]:
        """Get a user by ID."""
        query = """
            SELECT id, email, full_name, hashed_password, is_active, created_at, updated_at
            FROM users
            WHERE id = $1
        """

        try:
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, user_id)

                if record:
                    return UserInDB(**dict(record))
                return None

        except Exception as e:
            logger.error(f"Error getting user by ID {user_id}: {e}")
            raise

    async def update_user(
        self, user_id: int, user_data: UserUpdate
    ) -> Optional[UserInDB]:
        """Update user information."""
        # Build dynamic update query based on provided fields
        update_fields = []
        values = []
        param_count = 1

        if user_data.email is not None:
            update_fields.append(f"email = ${param_count}")
            values.append(user_data.email)
            param_count += 1

        if user_data.full_name is not None:
            update_fields.append(f"full_name = ${param_count}")
            values.append(user_data.full_name)
            param_count += 1

        if user_data.is_active is not None:
            update_fields.append(f"is_active = ${param_count}")
            values.append(user_data.is_active)
            param_count += 1

        if not update_fields:
            # No fields to update
            return await self.get_user_by_id(user_id)

        # Add updated_at field
        update_fields.append(f"updated_at = ${param_count}")
        values.append(datetime.utcnow())
        param_count += 1

        # Add user_id for WHERE clause
        values.append(user_id)

        query = f"""
            UPDATE users
            SET {", ".join(update_fields)}
            WHERE id = ${param_count}
            RETURNING id, email, full_name, hashed_password, is_active, created_at, updated_at
        """

        try:
            async with self.db_manager.get_connection() as connection:
                record = await connection.fetchrow(query, *values)

                if record:
                    return UserInDB(**dict(record))
                return None

        except asyncpg.UniqueViolationError:
            logger.warning(f"Attempt to update user {user_id} with existing email")
            raise ValueError("User with this email already exists")
        except Exception as e:
            logger.error(f"Error updating user {user_id}: {e}")
            raise

    async def update_password(self, user_id: int, hashed_password: str) -> bool:
        """Update user password."""
        query = """
            UPDATE users
            SET hashed_password = $1, updated_at = $2
            WHERE id = $3
        """

        try:
            async with self.db_manager.get_connection() as connection:
                result = await connection.execute(
                    query, hashed_password, datetime.utcnow(), user_id
                )

                # Check if any row was updated
                return result.split()[-1] == "1"

        except Exception as e:
            logger.error(f"Error updating password for user {user_id}: {e}")
            raise

    async def delete_user(self, user_id: int) -> bool:
        """Delete a user (soft delete by setting is_active to False)."""
        query = """
            UPDATE users
            SET is_active = FALSE, updated_at = $1
            WHERE id = $2
        """

        try:
            async with self.db_manager.get_connection() as connection:
                result = await connection.execute(query, datetime.utcnow(), user_id)

                # Check if any row was updated
                return result.split()[-1] == "1"

        except Exception as e:
            logger.error(f"Error deleting user {user_id}: {e}")
            raise

    async def is_email_taken(
        self, email: str, exclude_user_id: Optional[int] = None
    ) -> bool:
        """Check if an email is already taken by another user."""
        if exclude_user_id:
            query = "SELECT 1 FROM users WHERE email = $1 AND id != $2"
            params = [email, exclude_user_id]
        else:
            query = "SELECT 1 FROM users WHERE email = $1"
            params = [email]

        try:
            async with self.db_manager.get_connection() as connection:
                result = await connection.fetchval(query, *params)
                return result is not None

        except Exception as e:
            logger.error(f"Error checking if email {email} is taken: {e}")
            raise
