"""
Structured logging configuration using structlog.

This module sets up comprehensive logging for the warehouse management system
with structured JSON logging for production and human-readable console logging
for development.
"""

import logging
import sys

import structlog
from structlog.stdlib import LoggerFactory

from app.core.config import settings


def setup_logging():
    """Configure structured logging for the application."""

    # Configure structlog
    structlog.configure(
        processors=[
            # Add log level and timestamp
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            # Choose output format based on configuration
            _get_renderer(),
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.LOG_LEVEL.upper()),
    )

    # Set specific logger levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("asyncpg").setLevel(logging.WARNING)


def _get_renderer():
    """Get the appropriate log renderer based on configuration."""
    if settings.LOG_FORMAT.lower() == "json":
        return structlog.processors.JSONRenderer()
    else:
        return structlog.dev.ConsoleRenderer(colors=True)


def get_logger(name: str = None) -> structlog.stdlib.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


class LoggingMiddleware:
    """Middleware for request/response logging."""

    def __init__(self, app):
        self.app = app
        self.logger = get_logger("middleware.logging")

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        # Extract request information
        headers = scope.get("headers", [])
        headers_dict = {
            key.decode(): value.decode()
            for key, value in headers
            if isinstance(key, bytes) and isinstance(value, bytes)
        }

        request_info = {
            "method": scope["method"],
            "path": scope["path"],
            "query_string": scope.get("query_string", b"").decode(),
            "client": scope.get("client"),
            "headers": headers_dict,
        }

        # Log request
        self.logger.info("Request started", **request_info)

        # Process request and capture response
        response_info = {"status_code": None}

        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                response_info["status_code"] = message["status"]
            await send(message)

        try:
            await self.app(scope, receive, send_wrapper)
            self.logger.info(
                "Request completed",
                method=request_info["method"],
                path=request_info["path"],
                status_code=response_info["status_code"],
            )
        except Exception as e:
            self.logger.error(
                "Request failed",
                method=request_info["method"],
                path=request_info["path"],
                error=str(e),
                exc_info=True,
            )
            raise


# Request ID middleware for tracing
class RequestIDMiddleware:
    """Middleware to add request ID for tracing."""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return

        import uuid

        request_id = str(uuid.uuid4())

        # Add request ID to scope
        scope["request_id"] = request_id

        # Add request ID to response headers
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                headers = list(message.get("headers", []))
                headers.append((b"x-request-id", request_id.encode()))
                message["headers"] = headers
            await send(message)

        # Add request ID to logging context
        structlog.contextvars.clear_contextvars()
        structlog.contextvars.bind_contextvars(request_id=request_id)

        await self.app(scope, receive, send_wrapper)
