"""
Database configuration and connection management using asyncpg.

This module provides async database connection pooling, transaction management,
and database utilities for the warehouse management system.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

import asyncpg
from asyncpg import Pool

from app.core.config import settings

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database connection manager with connection pooling."""
    
    def __init__(self):
        self._pool: Optional[Pool] = None
        self._lock = asyncio.Lock()
    
    async def create_pool(self) -> Pool:
        """Create and return a connection pool."""
        if self._pool is None:
            async with self._lock:
                if self._pool is None:
                    logger.info("Creating database connection pool")
                    try:
                        self._pool = await asyncpg.create_pool(
                            str(settings.DATABASE_URL),
                            min_size=settings.DB_POOL_MIN_SIZE,
                            max_size=settings.DB_POOL_MAX_SIZE,
                            max_queries=settings.DB_POOL_MAX_QUERIES,
                            max_inactive_connection_lifetime=settings.DB_POOL_MAX_INACTIVE_CONNECTION_LIFETIME,
                            command_timeout=60,
                        )
                        logger.info(
                            f"Database pool created with {settings.DB_POOL_MIN_SIZE}-{settings.DB_POOL_MAX_SIZE} connections"
                        )
                    except Exception as e:
                        logger.error(f"Failed to create database pool: {e}")
                        raise
        return self._pool
    
    async def close_pool(self):
        """Close the database connection pool."""
        if self._pool:
            logger.info("Closing database connection pool")
            await self._pool.close()
            self._pool = None
    
    async def get_pool(self) -> Pool:
        """Get the database connection pool."""
        if self._pool is None:
            await self.create_pool()
        return self._pool
    
    @asynccontextmanager
    async def get_connection(self) -> AsyncGenerator[asyncpg.Connection, None]:
        """Get a database connection from the pool."""
        pool = await self.get_pool()
        async with pool.acquire() as connection:
            yield connection
    
    @asynccontextmanager
    async def get_transaction(self) -> AsyncGenerator[asyncpg.Connection, None]:
        """Get a database connection with transaction management."""
        pool = await self.get_pool()
        async with pool.acquire() as connection:
            async with connection.transaction():
                yield connection
    
    async def execute_query(self, query: str, *args) -> str:
        """Execute a query and return the result."""
        async with self.get_connection() as connection:
            return await connection.execute(query, *args)
    
    async def fetch_one(self, query: str, *args) -> Optional[asyncpg.Record]:
        """Fetch a single record."""
        async with self.get_connection() as connection:
            return await connection.fetchrow(query, *args)
    
    async def fetch_all(self, query: str, *args) -> list[asyncpg.Record]:
        """Fetch all records."""
        async with self.get_connection() as connection:
            return await connection.fetch(query, *args)
    
    async def fetch_val(self, query: str, *args):
        """Fetch a single value."""
        async with self.get_connection() as connection:
            return await connection.fetchval(query, *args)


# Global database manager instance
db_manager = DatabaseManager()


async def get_database() -> DatabaseManager:
    """Dependency to get database manager."""
    return db_manager


async def init_database():
    """Initialize database connection pool."""
    await db_manager.create_pool()
    logger.info("Database initialized successfully")


async def close_database():
    """Close database connection pool."""
    await db_manager.close_pool()
    logger.info("Database connections closed")


# Database utilities
async def check_database_connection() -> bool:
    """Check if database connection is working."""
    try:
        async with db_manager.get_connection() as connection:
            await connection.fetchval("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False


async def get_database_info() -> dict:
    """Get database information."""
    try:
        async with db_manager.get_connection() as connection:
            version = await connection.fetchval("SELECT version()")
            current_db = await connection.fetchval("SELECT current_database()")
            current_user = await connection.fetchval("SELECT current_user")
            
        return {
            "version": version,
            "database": current_db,
            "user": current_user,
            "status": "connected"
        }
    except Exception as e:
        logger.error(f"Failed to get database info: {e}")
        return {"status": "error", "error": str(e)}
