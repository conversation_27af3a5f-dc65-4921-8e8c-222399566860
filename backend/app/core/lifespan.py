"""
Application lifespan management.

This module handles application startup and shutdown events,
including database initialization and cleanup.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI

from app.core.database import init_database, close_database, check_database_connection
from app.core.logging import get_logger

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    
    # Startup
    logger.info("Starting warehouse management system")
    
    try:
        # Initialize database
        await init_database()
        
        # Check database connection
        if await check_database_connection():
            logger.info("Database connection verified")
        else:
            logger.error("Database connection failed")
            raise Exception("Database connection failed")
        
        logger.info("Application startup completed successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Application startup failed: {e}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down warehouse management system")
        
        try:
            await close_database()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        
        logger.info("Application shutdown completed")
