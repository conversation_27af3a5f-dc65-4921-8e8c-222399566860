"""
Middleware configuration for the FastAPI application.

This module sets up various middleware components including error handling,
logging, request ID tracking, and performance monitoring.
"""

import time
from typing import Callable

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.exceptions import AppException
from app.core.logging import LoggingMiddleware, RequestIDMiddleware, get_logger

logger = get_logger(__name__)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for centralized error handling."""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
        except AppException as e:
            logger.error(
                "Application error occurred",
                error_code=e.error_code,
                error_message=e.message,
                status_code=e.status_code,
                path=request.url.path,
                method=request.method,
            )
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "error": {
                        "code": e.error_code,
                        "message": e.message,
                        "details": e.details,
                    }
                },
            )
        except Exception as e:
            logger.error(
                "Unexpected error occurred",
                error=str(e),
                path=request.url.path,
                method=request.method,
                exc_info=True,
            )
            return JSONResponse(
                status_code=500,
                content={
                    "error": {
                        "code": "INTERNAL_SERVER_ERROR",
                        "message": "An unexpected error occurred",
                        "details": None,
                    }
                },
            )


class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware for performance monitoring and metrics."""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        response = await call_next(request)

        process_time = time.time() - start_time

        # Add performance headers
        response.headers["X-Process-Time"] = str(process_time)

        # Log slow requests
        if process_time > 1.0:  # Log requests taking more than 1 second
            logger.warning(
                "Slow request detected",
                path=request.url.path,
                method=request.method,
                process_time=process_time,
                status_code=response.status_code,
            )

        # Log performance metrics
        logger.info(
            "Request performance",
            path=request.url.path,
            method=request.method,
            process_time=process_time,
            status_code=response.status_code,
        )

        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers."""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)

        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        # Allow external resources for Swagger UI documentation
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "img-src 'self' data: https://fastapi.tiangolo.com; "
            "font-src 'self' https://cdn.jsdelivr.net"
        )

        return response


def setup_middleware(app: FastAPI) -> None:
    """Set up all middleware for the FastAPI application."""

    # Add trusted host middleware (should be first)
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"],  # Configure appropriately for production
    )

    # Add security headers middleware
    app.add_middleware(SecurityHeadersMiddleware)

    # Add performance monitoring middleware
    app.add_middleware(PerformanceMiddleware)

    # Add error handling middleware
    app.add_middleware(ErrorHandlingMiddleware)

    # Add request ID middleware
    app.add_middleware(RequestIDMiddleware)

    # Add logging middleware (should be last to capture all request info)
    app.add_middleware(LoggingMiddleware)

    logger.info("Middleware setup completed")
