"""
Custom exception classes for the warehouse management system.

This module defines application-specific exceptions with proper error codes,
HTTP status codes, and structured error information.
"""

from typing import Any, Dict, Optional


class AppException(Exception):
    """Base application exception class."""
    
    def __init__(
        self,
        message: str,
        error_code: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(AppException):
    """Exception for validation errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=422,
            details=details,
        )


class NotFoundError(AppException):
    """Exception for resource not found errors."""
    
    def __init__(self, resource: str, identifier: str):
        super().__init__(
            message=f"{resource} with identifier '{identifier}' not found",
            error_code="RESOURCE_NOT_FOUND",
            status_code=404,
            details={"resource": resource, "identifier": identifier},
        )


class ConflictError(AppException):
    """Exception for resource conflict errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="RESOURCE_CONFLICT",
            status_code=409,
            details=details,
        )


class DatabaseError(AppException):
    """Exception for database-related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            status_code=500,
            details=details,
        )


class AuthenticationError(AppException):
    """Exception for authentication errors."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=401,
        )


class AuthorizationError(AppException):
    """Exception for authorization errors."""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=403,
        )


class BusinessLogicError(AppException):
    """Exception for business logic violations."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="BUSINESS_LOGIC_ERROR",
            status_code=400,
            details=details,
        )


class ExternalServiceError(AppException):
    """Exception for external service errors."""
    
    def __init__(self, service: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"External service '{service}' error: {message}",
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=502,
            details={"service": service, **(details or {})},
        )


class RateLimitError(AppException):
    """Exception for rate limiting errors."""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            status_code=429,
        )


# Warehouse-specific exceptions
class PickError(BusinessLogicError):
    """Exception for pick operation errors."""
    
    def __init__(self, pick_id: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Pick operation error for {pick_id}: {message}",
            details={"pick_id": pick_id, **(details or {})},
        )


class InventoryError(BusinessLogicError):
    """Exception for inventory operation errors."""
    
    def __init__(self, item_sku: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Inventory error for {item_sku}: {message}",
            details={"item_sku": item_sku, **(details or {})},
        )


class VehicleError(BusinessLogicError):
    """Exception for vehicle operation errors."""
    
    def __init__(self, vehicle_id: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Vehicle operation error for {vehicle_id}: {message}",
            details={"vehicle_id": vehicle_id, **(details or {})},
        )


class ShipmentError(BusinessLogicError):
    """Exception for shipment operation errors."""
    
    def __init__(self, shipment_id: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"Shipment operation error for {shipment_id}: {message}",
            details={"shipment_id": shipment_id, **(details or {})},
        )
