"""
Main API router that includes all endpoint routers.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    failed_messages,
    inventory_transactions,
    operator_events,
    picks,
    shipments,
    vehicle_movements,
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(picks.router, prefix="/picks", tags=["picks"])
api_router.include_router(
    vehicle_movements.router, prefix="/vehicle-movements", tags=["vehicle-movements"]
)
api_router.include_router(
    operator_events.router, prefix="/operator-events", tags=["operator-events"]
)
api_router.include_router(
    inventory_transactions.router,
    prefix="/inventory-transactions",
    tags=["inventory-transactions"],
)
api_router.include_router(shipments.router, prefix="/shipments", tags=["shipments"])
api_router.include_router(
    failed_messages.router, prefix="/failed-messages", tags=["failed-messages"]
)
