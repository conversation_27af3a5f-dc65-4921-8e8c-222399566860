"""
API endpoints for operator events.

This module provides RESTful endpoints for managing operator events
including performance tracking, activity logging, and operator-specific analytics.
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from fastapi.responses import JSONResponse

from app.core.database import DatabaseManager, get_database
from app.core.exceptions import DatabaseError, NotFoundError, ValidationError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.operator_events import (
    OperatorEventCreate,
    OperatorEventFilterParams,
    OperatorEventResponse,
    OperatorEventUpdate,
    OperatorStatsResponse,
)
from app.repositories.operator_events import OperatorEventRepository

logger = get_logger(__name__)
router = APIRouter()


async def get_operator_event_repository(
    db: DatabaseManager = Depends(get_database),
) -> OperatorEventRepository:
    """Dependency to get operator event repository."""
    return OperatorEventRepository(db)


@router.post("/", response_model=OperatorEventResponse, status_code=201)
async def create_operator_event(
    event_data: OperatorEventCreate,
    repository: OperatorEventRepository = Depends(get_operator_event_repository),
):
    """Create a new operator event."""
    try:
        logger.info("Creating new operator event", event_id=event_data.event_id)

        # Check if event already exists
        existing_event = await repository.get_by_field("event_id", event_data.event_id)
        if existing_event:
            raise ValidationError(f"Event with ID {event_data.event_id} already exists")

        event = await repository.create(event_data)
        logger.info(
            "Operator event created successfully", event_id=event.event_id, id=event.id
        )

        return event

    except ValidationError as e:
        logger.warning("Validation error creating operator event", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error creating operator event", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to create operator event")
    except Exception as e:
        logger.error(
            "Unexpected error creating operator event", error=str(e), exc_info=True
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics", response_model=OperatorStatsResponse)
async def get_operator_statistics(
    start_date: Optional[datetime] = Query(
        None, description="Start date for statistics"
    ),
    end_date: Optional[datetime] = Query(None, description="End date for statistics"),
    department: Optional[str] = Query(None, description="Filter by department"),
    repository: OperatorEventRepository = Depends(get_operator_event_repository),
):
    """Get operator statistics."""
    try:
        stats = await repository.get_operator_statistics(
            start_date, end_date, department
        )
        logger.info("Retrieved operator statistics")

        return stats
    except DatabaseError as e:
        logger.error("Database error getting operator statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(
            "Unexpected error getting operator statistics",
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{event_id}", response_model=OperatorEventResponse)
async def get_operator_event(
    event_id: str = Path(..., description="Event ID"),
    repository: OperatorEventRepository = Depends(get_operator_event_repository),
):
    """Get an operator event by ID."""
    try:
        event = await repository.get_by_field("event_id", event_id)
        if not event:
            raise NotFoundError("Operator Event", event_id)

        return event

    except NotFoundError as e:
        logger.warning("Operator event not found", event_id=event_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error getting operator event", event_id=event_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to get operator event")
    except Exception as e:
        logger.error(
            "Unexpected error getting operator event",
            event_id=event_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{event_id}", response_model=OperatorEventResponse)
async def update_operator_event(
    event_id: str = Path(..., description="Event ID"),
    event_data: OperatorEventUpdate = None,
    repository: OperatorEventRepository = Depends(get_operator_event_repository),
):
    """Update an operator event."""
    try:
        # Get existing event
        existing_event = await repository.get_by_field("event_id", event_id)
        if not existing_event:
            raise NotFoundError("Operator Event", event_id)

        # Update event
        updated_event = await repository.update(existing_event.id, event_data)
        logger.info("Operator event updated successfully", event_id=event_id)

        return updated_event

    except NotFoundError as e:
        logger.warning("Operator event not found for update", event_id=event_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error updating operator event", event_id=event_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to update operator event")
    except Exception as e:
        logger.error(
            "Unexpected error updating operator event",
            event_id=event_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{event_id}", status_code=204)
async def delete_operator_event(
    event_id: str = Path(..., description="Event ID"),
    repository: OperatorEventRepository = Depends(get_operator_event_repository),
):
    """Delete an operator event."""
    try:
        # Get existing event
        existing_event = await repository.get_by_field("event_id", event_id)
        if not existing_event:
            raise NotFoundError("Operator Event", event_id)

        # Delete event
        success = await repository.delete(existing_event.id)
        if not success:
            raise DatabaseError("Failed to delete operator event")

        logger.info("Operator event deleted successfully", event_id=event_id)
        return JSONResponse(status_code=204, content=None)

    except NotFoundError as e:
        logger.warning("Operator event not found for deletion", event_id=event_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error deleting operator event", event_id=event_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to delete operator event")
    except Exception as e:
        logger.error(
            "Unexpected error deleting operator event",
            event_id=event_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=PaginatedResponse)
async def list_operator_events(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    operator_id: Optional[str] = Query(None, description="Filter by operator ID"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    department: Optional[str] = Query(None, description="Filter by department"),
    start_date: Optional[datetime] = Query(
        None, description="Filter events after this date"
    ),
    end_date: Optional[datetime] = Query(
        None, description="Filter events before this date"
    ),
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: Optional[str] = Query("timestamp", description="Sort field"),
    sort_order: Optional[str] = Query(
        "desc", pattern="^(asc|desc)$", description="Sort order"
    ),
    repository: OperatorEventRepository = Depends(get_operator_event_repository),
):
    """List operator events with filtering and pagination."""
    try:
        pagination = PaginationParams(page=page, size=size)
        filters = OperatorEventFilterParams(
            operator_id=operator_id,
            event_type=event_type,
            department=department,
            start_date=start_date,
            end_date=end_date,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
        )

        result = await repository.list_with_filters(pagination, filters)
        logger.info("Listed operator events", total=result.total, page=page, size=size)

        return result

    except ValidationError as e:
        logger.warning("Validation error listing operator events", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error listing operator events", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list operator events")
    except Exception as e:
        logger.error(
            "Unexpected error listing operator events", error=str(e), exc_info=True
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/operator/{operator_id}", response_model=List[OperatorEventResponse])
async def get_events_by_operator(
    operator_id: str = Path(..., description="Operator ID"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of events to return"
    ),
    repository: OperatorEventRepository = Depends(get_operator_event_repository),
):
    """Get events for a specific operator."""
    try:
        events = await repository.get_by_field_multiple(
            "operator_id", operator_id, limit
        )
        logger.info(
            "Retrieved events by operator", operator_id=operator_id, count=len(events)
        )

        return events

    except DatabaseError as e:
        logger.error(
            "Database error getting events by operator",
            operator_id=operator_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to get events by operator")
    except Exception as e:
        logger.error(
            "Unexpected error getting events by operator",
            operator_id=operator_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/department/{department}", response_model=List[OperatorEventResponse])
async def get_events_by_department(
    department: str = Path(..., description="Department"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of events to return"
    ),
    repository: OperatorEventRepository = Depends(get_operator_event_repository),
):
    """Get events for a specific department."""
    try:
        events = await repository.get_by_field_multiple("department", department, limit)
        logger.info(
            "Retrieved events by department", department=department, count=len(events)
        )

        return events

    except DatabaseError as e:
        logger.error(
            "Database error getting events by department",
            department=department,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to get events by department"
        )
    except Exception as e:
        logger.error(
            "Unexpected error getting events by department",
            department=department,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")
