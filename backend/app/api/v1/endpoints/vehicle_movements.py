"""
API endpoints for vehicle movements.

This module provides RESTful endpoints for managing vehicle movements
including tracking, route efficiency metrics, and movement-specific operations.
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from fastapi.responses import JSONResponse

from app.core.database import DatabaseManager, get_database
from app.core.exceptions import DatabaseError, NotFoundError, ValidationError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.vehicle_movements import (
    VehicleMovementCreate,
    VehicleMovementFilterParams,
    VehicleMovementResponse,
    VehicleMovementStatsResponse,
    VehicleMovementUpdate,
)
from app.repositories.vehicle_movements import VehicleMovementRepository

logger = get_logger(__name__)
router = APIRouter()


async def get_vehicle_movement_repository(
    db: DatabaseManager = Depends(get_database),
) -> VehicleMovementRepository:
    """Dependency to get vehicle movement repository."""
    return VehicleMovementRepository(db)


@router.post("/", response_model=VehicleMovementResponse, status_code=201)
async def create_vehicle_movement(
    movement_data: VehicleMovementCreate,
    repository: VehicleMovementRepository = Depends(get_vehicle_movement_repository),
):
    """Create a new vehicle movement."""
    try:
        logger.info(
            "Creating new vehicle movement", movement_id=movement_data.movement_id
        )

        # Check if movement already exists
        existing_movement = await repository.get_by_movement_id(
            movement_data.movement_id
        )
        if existing_movement:
            raise ValidationError(
                f"Movement with ID {movement_data.movement_id} already exists"
            )

        movement = await repository.create(movement_data)
        logger.info(
            "Vehicle movement created successfully",
            movement_id=movement.movement_id,
            id=movement.id,
        )

        return movement

    except ValidationError as e:
        logger.warning("Validation error creating vehicle movement", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error creating vehicle movement", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to create vehicle movement")
    except Exception as e:
        logger.error(
            "Unexpected error creating vehicle movement", error=str(e), exc_info=True
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics", response_model=VehicleMovementStatsResponse)
async def get_vehicle_movement_statistics(
    start_date: Optional[datetime] = Query(
        None, description="Start date for statistics"
    ),
    end_date: Optional[datetime] = Query(None, description="End date for statistics"),
    vehicle_id: Optional[str] = Query(None, description="Filter by vehicle ID"),
    repository: VehicleMovementRepository = Depends(get_vehicle_movement_repository),
):
    """Get vehicle movement statistics."""
    try:
        stats = await repository.get_movement_statistics(
            start_date, end_date, vehicle_id
        )
        logger.info("Retrieved vehicle movement statistics")

        return stats

    except DatabaseError as e:
        logger.error("Database error getting vehicle movement statistics", error=str(e))
        raise HTTPException(
            status_code=500, detail="Failed to get vehicle movement statistics"
        )
    except Exception as e:
        logger.error(
            "Unexpected error getting vehicle movement statistics",
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{movement_id}", response_model=VehicleMovementResponse)
async def get_vehicle_movement(
    movement_id: str = Path(..., description="Movement ID"),
    repository: VehicleMovementRepository = Depends(get_vehicle_movement_repository),
):
    """Get a vehicle movement by ID."""
    try:
        movement = await repository.get_by_movement_id(movement_id)
        if not movement:
            raise NotFoundError("Vehicle Movement", movement_id)

        return movement

    except NotFoundError as e:
        logger.warning("Vehicle movement not found", movement_id=movement_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error getting vehicle movement",
            movement_id=movement_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to get vehicle movement")
    except Exception as e:
        logger.error(
            "Unexpected error getting vehicle movement",
            movement_id=movement_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{movement_id}", response_model=VehicleMovementResponse)
async def update_vehicle_movement(
    movement_id: str = Path(..., description="Movement ID"),
    movement_data: VehicleMovementUpdate = None,
    repository: VehicleMovementRepository = Depends(get_vehicle_movement_repository),
):
    """Update a vehicle movement."""
    try:
        # Get existing movement
        existing_movement = await repository.get_by_movement_id(movement_id)
        if not existing_movement:
            raise NotFoundError("Vehicle Movement", movement_id)

        # Update movement
        updated_movement = await repository.update(existing_movement.id, movement_data)
        logger.info("Vehicle movement updated successfully", movement_id=movement_id)

        return updated_movement

    except NotFoundError as e:
        logger.warning("Vehicle movement not found for update", movement_id=movement_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error updating vehicle movement",
            movement_id=movement_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to update vehicle movement")
    except Exception as e:
        logger.error(
            "Unexpected error updating vehicle movement",
            movement_id=movement_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{movement_id}", status_code=204)
async def delete_vehicle_movement(
    movement_id: str = Path(..., description="Movement ID"),
    repository: VehicleMovementRepository = Depends(get_vehicle_movement_repository),
):
    """Delete a vehicle movement."""
    try:
        # Get existing movement
        existing_movement = await repository.get_by_movement_id(movement_id)
        if not existing_movement:
            raise NotFoundError("Vehicle Movement", movement_id)

        # Delete movement
        success = await repository.delete(existing_movement.id)
        if not success:
            raise DatabaseError("Failed to delete vehicle movement")

        logger.info("Vehicle movement deleted successfully", movement_id=movement_id)
        return JSONResponse(status_code=204, content=None)

    except NotFoundError as e:
        logger.warning(
            "Vehicle movement not found for deletion", movement_id=movement_id
        )
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error deleting vehicle movement",
            movement_id=movement_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to delete vehicle movement")
    except Exception as e:
        logger.error(
            "Unexpected error deleting vehicle movement",
            movement_id=movement_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=PaginatedResponse)
async def list_vehicle_movements(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    vehicle_id: Optional[str] = Query(None, description="Filter by vehicle ID"),
    operator_id: Optional[str] = Query(None, description="Filter by operator ID"),
    vehicle_type: Optional[str] = Query(None, description="Filter by vehicle type"),
    movement_type: Optional[str] = Query(None, description="Filter by movement type"),
    start_date: Optional[datetime] = Query(
        None, description="Filter movements after this date"
    ),
    end_date: Optional[datetime] = Query(
        None, description="Filter movements before this date"
    ),
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: Optional[str] = Query("start_time", description="Sort field"),
    sort_order: Optional[str] = Query(
        "desc", pattern="^(asc|desc)$", description="Sort order"
    ),
    repository: VehicleMovementRepository = Depends(get_vehicle_movement_repository),
):
    """List vehicle movements with filtering and pagination."""
    try:
        pagination = PaginationParams(page=page, size=size)
        filters = VehicleMovementFilterParams(
            vehicle_id=vehicle_id,
            operator_id=operator_id,
            vehicle_type=vehicle_type,
            movement_type=movement_type,
            start_date=start_date,
            end_date=end_date,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
        )

        result = await repository.list_with_filters(pagination, filters)
        logger.info(
            "Listed vehicle movements", total=result.total, page=page, size=size
        )

        return result

    except ValidationError as e:
        logger.warning("Validation error listing vehicle movements", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error listing vehicle movements", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list vehicle movements")
    except Exception as e:
        logger.error(
            "Unexpected error listing vehicle movements", error=str(e), exc_info=True
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/vehicle/{vehicle_id}", response_model=List[VehicleMovementResponse])
async def get_movements_by_vehicle(
    vehicle_id: str = Path(..., description="Vehicle ID"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of movements to return"
    ),
    repository: VehicleMovementRepository = Depends(get_vehicle_movement_repository),
):
    """Get movements for a specific vehicle."""
    try:
        movements = await repository.get_movements_by_vehicle(vehicle_id, limit)
        logger.info(
            "Retrieved movements by vehicle",
            vehicle_id=vehicle_id,
            count=len(movements),
        )

        return movements

    except DatabaseError as e:
        logger.error(
            "Database error getting movements by vehicle",
            vehicle_id=vehicle_id,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to get movements by vehicle"
        )
    except Exception as e:
        logger.error(
            "Unexpected error getting movements by vehicle",
            vehicle_id=vehicle_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/operator/{operator_id}", response_model=List[VehicleMovementResponse])
async def get_movements_by_operator(
    operator_id: str = Path(..., description="Operator ID"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of movements to return"
    ),
    repository: VehicleMovementRepository = Depends(get_vehicle_movement_repository),
):
    """Get movements for a specific operator."""
    try:
        movements = await repository.get_movements_by_operator(operator_id, limit)
        logger.info(
            "Retrieved movements by operator",
            operator_id=operator_id,
            count=len(movements),
        )

        return movements

    except DatabaseError as e:
        logger.error(
            "Database error getting movements by operator",
            operator_id=operator_id,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to get movements by operator"
        )
    except Exception as e:
        logger.error(
            "Unexpected error getting movements by operator",
            operator_id=operator_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")
