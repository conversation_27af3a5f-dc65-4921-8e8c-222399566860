"""
API endpoints for shipments.

This module provides RESTful endpoints for managing shipments
including order processing, tracking, and shipment-specific business logic.
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from fastapi.responses import JSONResponse

from app.core.database import DatabaseManager, get_database
from app.core.exceptions import DatabaseError, NotFoundError, ValidationError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.shipments import (
    ShipmentCreate,
    ShipmentFilterParams,
    ShipmentResponse,
    ShipmentStatsResponse,
    ShipmentUpdate,
)
from app.repositories.shipments import ShipmentRepository

logger = get_logger(__name__)
router = APIRouter()


async def get_shipment_repository(
    db: DatabaseManager = Depends(get_database),
) -> ShipmentRepository:
    """Dependency to get shipment repository."""
    return ShipmentRepository(db)


@router.post("/", response_model=ShipmentResponse, status_code=201)
async def create_shipment(
    shipment_data: ShipmentCreate,
    repository: ShipmentRepository = Depends(get_shipment_repository),
):
    """Create a new shipment."""
    try:
        logger.info("Creating new shipment", shipment_id=shipment_data.shipment_id)

        # Check if shipment already exists
        existing_shipment = await repository.get_by_field(
            "shipment_id", shipment_data.shipment_id
        )
        if existing_shipment:
            raise ValidationError(
                f"Shipment with ID {shipment_data.shipment_id} already exists"
            )

        shipment = await repository.create(shipment_data)
        logger.info(
            "Shipment created successfully",
            shipment_id=shipment.shipment_id,
            id=shipment.id,
        )

        return shipment

    except ValidationError as e:
        logger.warning("Validation error creating shipment", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error creating shipment", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to create shipment")
    except Exception as e:
        logger.error("Unexpected error creating shipment", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics", response_model=ShipmentStatsResponse)
async def get_shipment_statistics(
    start_date: Optional[datetime] = Query(
        None, description="Start date for statistics"
    ),
    end_date: Optional[datetime] = Query(None, description="End date for statistics"),
    carrier: Optional[str] = Query(None, description="Filter by carrier"),
    repository: ShipmentRepository = Depends(get_shipment_repository),
):
    """Get shipment statistics."""
    try:
        stats = await repository.get_shipment_statistics(start_date, end_date, carrier)
        logger.info("Retrieved shipment statistics")

        return stats
    except DatabaseError as e:
        logger.error("Database error getting shipment statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(
            "Unexpected error getting shipment statistics",
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{shipment_id}", response_model=ShipmentResponse)
async def get_shipment(
    shipment_id: str = Path(..., description="Shipment ID"),
    repository: ShipmentRepository = Depends(get_shipment_repository),
):
    """Get a shipment by ID."""
    try:
        shipment = await repository.get_by_field("shipment_id", shipment_id)
        if not shipment:
            raise NotFoundError("Shipment", shipment_id)

        return shipment

    except NotFoundError as e:
        logger.warning("Shipment not found", shipment_id=shipment_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error getting shipment", shipment_id=shipment_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to get shipment")
    except Exception as e:
        logger.error(
            "Unexpected error getting shipment",
            shipment_id=shipment_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{shipment_id}", response_model=ShipmentResponse)
async def update_shipment(
    shipment_id: str = Path(..., description="Shipment ID"),
    shipment_data: ShipmentUpdate = None,
    repository: ShipmentRepository = Depends(get_shipment_repository),
):
    """Update a shipment."""
    try:
        # Get existing shipment
        existing_shipment = await repository.get_by_field("shipment_id", shipment_id)
        if not existing_shipment:
            raise NotFoundError("Shipment", shipment_id)

        # Update shipment
        updated_shipment = await repository.update(existing_shipment.id, shipment_data)
        logger.info("Shipment updated successfully", shipment_id=shipment_id)

        return updated_shipment

    except NotFoundError as e:
        logger.warning("Shipment not found for update", shipment_id=shipment_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error updating shipment", shipment_id=shipment_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to update shipment")
    except Exception as e:
        logger.error(
            "Unexpected error updating shipment",
            shipment_id=shipment_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{shipment_id}", status_code=204)
async def delete_shipment(
    shipment_id: str = Path(..., description="Shipment ID"),
    repository: ShipmentRepository = Depends(get_shipment_repository),
):
    """Delete a shipment."""
    try:
        # Get existing shipment
        existing_shipment = await repository.get_by_field("shipment_id", shipment_id)
        if not existing_shipment:
            raise NotFoundError("Shipment", shipment_id)

        # Delete shipment
        success = await repository.delete(existing_shipment.id)
        if not success:
            raise DatabaseError("Failed to delete shipment")

        logger.info("Shipment deleted successfully", shipment_id=shipment_id)
        return JSONResponse(status_code=204, content=None)

    except NotFoundError as e:
        logger.warning("Shipment not found for deletion", shipment_id=shipment_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error deleting shipment", shipment_id=shipment_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to delete shipment")
    except Exception as e:
        logger.error(
            "Unexpected error deleting shipment",
            shipment_id=shipment_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=PaginatedResponse)
async def list_shipments(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    order_id: Optional[str] = Query(None, description="Filter by order ID"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    shipment_status: Optional[str] = Query(
        None, description="Filter by shipment status"
    ),
    carrier: Optional[str] = Query(None, description="Filter by carrier"),
    start_date: Optional[datetime] = Query(
        None, description="Filter shipments after this date"
    ),
    end_date: Optional[datetime] = Query(
        None, description="Filter shipments before this date"
    ),
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: Optional[str] = Query("created_time", description="Sort field"),
    sort_order: Optional[str] = Query(
        "desc", pattern="^(asc|desc)$", description="Sort order"
    ),
    repository: ShipmentRepository = Depends(get_shipment_repository),
):
    """List shipments with filtering and pagination."""
    try:
        pagination = PaginationParams(page=page, size=size)
        filters = ShipmentFilterParams(
            order_id=order_id,
            customer_id=customer_id,
            shipment_status=shipment_status,
            carrier=carrier,
            start_date=start_date,
            end_date=end_date,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
        )

        result = await repository.list_with_filters(pagination, filters)
        logger.info("Listed shipments", total=result.total, page=page, size=size)

        return result

    except ValidationError as e:
        logger.warning("Validation error listing shipments", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error listing shipments", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list shipments")
    except Exception as e:
        logger.error("Unexpected error listing shipments", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/customer/{customer_id}", response_model=List[ShipmentResponse])
async def get_shipments_by_customer(
    customer_id: str = Path(..., description="Customer ID"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of shipments to return"
    ),
    repository: ShipmentRepository = Depends(get_shipment_repository),
):
    """Get shipments for a specific customer."""
    try:
        shipments = await repository.get_by_field_multiple(
            "customer_id", customer_id, limit
        )
        logger.info(
            "Retrieved shipments by customer",
            customer_id=customer_id,
            count=len(shipments),
        )

        return shipments

    except DatabaseError as e:
        logger.error(
            "Database error getting shipments by customer",
            customer_id=customer_id,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to get shipments by customer"
        )
    except Exception as e:
        logger.error(
            "Unexpected error getting shipments by customer",
            customer_id=customer_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/order/{order_id}", response_model=List[ShipmentResponse])
async def get_shipments_by_order(
    order_id: str = Path(..., description="Order ID"),
    repository: ShipmentRepository = Depends(get_shipment_repository),
):
    """Get shipments for a specific order."""
    try:
        shipments = await repository.get_by_field_multiple("order_id", order_id, 100)
        logger.info(
            "Retrieved shipments by order", order_id=order_id, count=len(shipments)
        )

        return shipments

    except DatabaseError as e:
        logger.error(
            "Database error getting shipments by order", order_id=order_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to get shipments by order")
    except Exception as e:
        logger.error(
            "Unexpected error getting shipments by order",
            order_id=order_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/tracking/{tracking_number}", response_model=ShipmentResponse)
async def get_shipment_by_tracking(
    tracking_number: str = Path(..., description="Tracking Number"),
    repository: ShipmentRepository = Depends(get_shipment_repository),
):
    """Get shipment by tracking number."""
    try:
        shipment = await repository.get_by_field("tracking_number", tracking_number)
        if not shipment:
            raise NotFoundError("Shipment", tracking_number)

        return shipment

    except NotFoundError as e:
        logger.warning(
            "Shipment not found by tracking number", tracking_number=tracking_number
        )
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error getting shipment by tracking",
            tracking_number=tracking_number,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to get shipment by tracking"
        )
    except Exception as e:
        logger.error(
            "Unexpected error getting shipment by tracking",
            tracking_number=tracking_number,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")
