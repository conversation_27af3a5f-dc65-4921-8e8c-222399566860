"""
API endpoints for pick operations.

This module provides RESTful endpoints for managing warehouse pick operations
including CRUD operations, filtering, pagination, and pick-specific business logic.
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from fastapi.responses import JSONResponse

from app.core.database import DatabaseManager, get_database
from app.core.exceptions import DatabaseError, NotFoundError, ValidationError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.picks import (
    PickCreate,
    PickFilterParams,
    PickResponse,
    PickStatsResponse,
    PickUpdate,
)
from app.repositories.picks import PickRepository

logger = get_logger(__name__)
router = APIRouter()


async def get_pick_repository(
    db: DatabaseManager = Depends(get_database),
) -> PickRepository:
    """Dependency to get pick repository."""
    return PickRepository(db)


@router.post("/", response_model=PickResponse, status_code=201)
async def create_pick(
    pick_data: PickCreate, repository: PickRepository = Depends(get_pick_repository)
):
    """Create a new pick operation."""
    try:
        logger.info("Creating new pick", pick_id=pick_data.pick_id)

        # Check if pick already exists
        existing_pick = await repository.get_by_pick_id(pick_data.pick_id)
        if existing_pick:
            raise ValidationError(f"Pick with ID {pick_data.pick_id} already exists")

        pick = await repository.create(pick_data)
        logger.info("Pick created successfully", pick_id=pick.pick_id, id=pick.id)

        return pick

    except ValidationError as e:
        logger.warning("Validation error creating pick", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error creating pick", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to create pick")
    except Exception as e:
        logger.error("Unexpected error creating pick", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics", response_model=PickStatsResponse)
async def get_pick_statistics(
    start_date: Optional[datetime] = Query(
        None, description="Start date for statistics"
    ),
    end_date: Optional[datetime] = Query(None, description="End date for statistics"),
    operator_id: Optional[str] = Query(None, description="Filter by operator ID"),
    repository: PickRepository = Depends(get_pick_repository),
):
    """Get pick statistics."""
    try:
        stats = await repository.get_pick_statistics(start_date, end_date, operator_id)
        logger.info("Retrieved pick statistics")

        return stats

    except DatabaseError as e:
        logger.error("Database error getting pick statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get pick statistics")
    except Exception as e:
        logger.error(
            "Unexpected error getting pick statistics", error=str(e), exc_info=True
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{pick_id}", response_model=PickResponse)
async def get_pick(
    pick_id: str = Path(..., description="Pick ID"),
    repository: PickRepository = Depends(get_pick_repository),
):
    """Get a pick by ID."""
    try:
        pick = await repository.get_by_pick_id(pick_id)
        if not pick:
            raise NotFoundError("Pick", pick_id)

        return pick

    except NotFoundError as e:
        logger.warning("Pick not found", pick_id=pick_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error getting pick", pick_id=pick_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get pick")
    except Exception as e:
        logger.error(
            "Unexpected error getting pick",
            pick_id=pick_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{pick_id}", response_model=PickResponse)
async def update_pick(
    pick_id: str = Path(..., description="Pick ID"),
    pick_data: PickUpdate = None,
    repository: PickRepository = Depends(get_pick_repository),
):
    """Update a pick."""
    try:
        # Get existing pick
        existing_pick = await repository.get_by_pick_id(pick_id)
        if not existing_pick:
            raise NotFoundError("Pick", pick_id)

        # Update pick
        updated_pick = await repository.update(existing_pick.id, pick_data)
        logger.info("Pick updated successfully", pick_id=pick_id)

        return updated_pick

    except NotFoundError as e:
        logger.warning("Pick not found for update", pick_id=pick_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error updating pick", pick_id=pick_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to update pick")
    except Exception as e:
        logger.error(
            "Unexpected error updating pick",
            pick_id=pick_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{pick_id}", status_code=204)
async def delete_pick(
    pick_id: str = Path(..., description="Pick ID"),
    repository: PickRepository = Depends(get_pick_repository),
):
    """Delete a pick."""
    try:
        # Get existing pick
        existing_pick = await repository.get_by_pick_id(pick_id)
        if not existing_pick:
            raise NotFoundError("Pick", pick_id)

        # Delete pick
        success = await repository.delete(existing_pick.id)
        if not success:
            raise DatabaseError("Failed to delete pick")

        logger.info("Pick deleted successfully", pick_id=pick_id)
        return JSONResponse(status_code=204, content=None)

    except NotFoundError as e:
        logger.warning("Pick not found for deletion", pick_id=pick_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error deleting pick", pick_id=pick_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to delete pick")
    except Exception as e:
        logger.error(
            "Unexpected error deleting pick",
            pick_id=pick_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=PaginatedResponse)
async def list_picks(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    operator_id: Optional[str] = Query(None, description="Filter by operator ID"),
    item_sku: Optional[str] = Query(None, description="Filter by item SKU"),
    pick_status: Optional[str] = Query(None, description="Filter by pick status"),
    priority: Optional[str] = Query(None, description="Filter by priority"),
    zone: Optional[str] = Query(None, description="Filter by zone"),
    batch_id: Optional[str] = Query(None, description="Filter by batch ID"),
    start_date: Optional[datetime] = Query(
        None, description="Filter picks after this date"
    ),
    end_date: Optional[datetime] = Query(
        None, description="Filter picks before this date"
    ),
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: Optional[str] = Query("start_time", description="Sort field"),
    sort_order: Optional[str] = Query(
        "desc", pattern="^(asc|desc)$", description="Sort order"
    ),
    repository: PickRepository = Depends(get_pick_repository),
):
    """List picks with filtering and pagination."""
    try:
        pagination = PaginationParams(page=page, size=size)
        filters = PickFilterParams(
            operator_id=operator_id,
            item_sku=item_sku,
            pick_status=pick_status,
            priority=priority,
            zone=zone,
            batch_id=batch_id,
            start_date=start_date,
            end_date=end_date,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
        )

        result = await repository.list_with_filters(pagination, filters)
        logger.info("Listed picks", total=result.total, page=page, size=size)

        return result

    except ValidationError as e:
        logger.warning("Validation error listing picks", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error listing picks", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list picks")
    except Exception as e:
        logger.error("Unexpected error listing picks", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/operator/{operator_id}", response_model=List[PickResponse])
async def get_picks_by_operator(
    operator_id: str = Path(..., description="Operator ID"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of picks to return"
    ),
    repository: PickRepository = Depends(get_pick_repository),
):
    """Get picks for a specific operator."""
    try:
        picks = await repository.get_picks_by_operator(operator_id, limit)
        logger.info(
            "Retrieved picks by operator", operator_id=operator_id, count=len(picks)
        )

        return picks

    except DatabaseError as e:
        logger.error(
            "Database error getting picks by operator",
            operator_id=operator_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to get picks by operator")
    except Exception as e:
        logger.error(
            "Unexpected error getting picks by operator",
            operator_id=operator_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/batch/{batch_id}", response_model=List[PickResponse])
async def get_picks_by_batch(
    batch_id: str = Path(..., description="Batch ID"),
    repository: PickRepository = Depends(get_pick_repository),
):
    """Get all picks in a batch."""
    try:
        picks = await repository.get_picks_by_batch(batch_id)
        logger.info("Retrieved picks by batch", batch_id=batch_id, count=len(picks))

        return picks

    except DatabaseError as e:
        logger.error(
            "Database error getting picks by batch", batch_id=batch_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to get picks by batch")
    except Exception as e:
        logger.error(
            "Unexpected error getting picks by batch",
            batch_id=batch_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")
