"""
Authentication endpoints for user registration, login, and logout.

This module provides authentication endpoints including user registration,
login, logout, and password management for the warehouse management system.
"""

import logging

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials

from app.api.dependencies import get_current_user, get_user_repository, security
from app.core.auth import generate_token_response, get_password_hash, verify_password
from app.models.users import (
    PasswordReset,
    PasswordResetConfirm,
    Token,
    User,
    UserCreate,
    UserInDB,
    UserLogin,
)
from app.repositories.users import UserRepository

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/signup", response_model=User, status_code=status.HTTP_201_CREATED)
async def signup(
    user_data: UserCreate,
    user_repository: UserRepository = Depends(get_user_repository),
):
    """
    Register a new user.

    Creates a new user account with email and password.
    Email must be unique across all users.
    """
    try:
        # Check if user already exists
        existing_user = await user_repository.get_user_by_email(user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists",
            )

        # Hash the password
        hashed_password = get_password_hash(user_data.password)

        # Create the user
        created_user = await user_repository.create_user(user_data, hashed_password)

        # Convert to User model (without password)
        return User(
            id=created_user.id,
            email=created_user.email,
            full_name=created_user.full_name,
            is_active=created_user.is_active,
            created_at=created_user.created_at,
            updated_at=created_user.updated_at,
        )

    except ValueError as e:
        logger.warning(f"User registration failed: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error during user registration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/login", response_model=Token)
async def login(
    user_credentials: UserLogin,
    user_repository: UserRepository = Depends(get_user_repository),
):
    """
    Authenticate user and return access token.

    Validates user credentials and returns a JWT access token
    that can be used for subsequent authenticated requests.
    """
    try:
        # Get user by email
        user = await user_repository.get_user_by_email(user_credentials.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Check if user is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is inactive",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Verify password
        if not verify_password(user_credentials.password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Generate token response
        token_response = generate_token_response(user.id, user.email)

        logger.info(f"User {user.email} logged in successfully")
        return Token(**token_response)

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: UserInDB = Depends(get_current_user),
):
    """
    Logout current user.

    Note: Since we're using stateless JWT tokens, logout is handled
    client-side by removing the token. This endpoint serves as a
    confirmation and for potential future token blacklisting.
    """
    logger.info(f"User {current_user.email} logged out")
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=User)
async def get_current_user_info(
    current_user: UserInDB = Depends(get_current_user),
):
    """
    Get current user information.

    Returns the profile information of the currently authenticated user.
    """
    return User(
        id=current_user.id,
        email=current_user.email,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
    )


@router.post("/password-reset")
async def request_password_reset(
    password_reset: PasswordReset,
    user_repository: UserRepository = Depends(get_user_repository),
):
    """
    Request password reset.

    Note: This is a placeholder implementation. In a production system,
    this would send a password reset email with a secure token.
    """
    try:
        # Check if user exists
        user = await user_repository.get_user_by_email(password_reset.email)
        if not user:
            # Don't reveal whether the email exists or not
            return {
                "message": "If the email exists, a password reset link has been sent"
            }

        # In a real implementation, you would:
        # 1. Generate a secure password reset token
        # 2. Store it in the database with expiration
        # 3. Send an email with the reset link

        logger.info(f"Password reset requested for {password_reset.email}")
        return {"message": "If the email exists, a password reset link has been sent"}

    except Exception as e:
        logger.error(f"Error during password reset request: {e}")
        return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/password-reset/confirm")
async def confirm_password_reset(
    password_reset_confirm: PasswordResetConfirm,
    user_repository: UserRepository = Depends(get_user_repository),
):
    """
    Confirm password reset with token.

    Validates the reset token and updates the user's password.
    """
    try:
        # Verify the password reset token
        from app.core.auth import get_password_hash, verify_password_reset_token

        email = verify_password_reset_token(password_reset_confirm.token)
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired password reset token",
            )

        # Get user by email
        user = await user_repository.get_user_by_email(email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
            )

        # Check if user is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User account is inactive",
            )

        # Hash the new password
        hashed_password = get_password_hash(password_reset_confirm.new_password)

        # Update the user's password
        success = await user_repository.update_password(user.id, hashed_password)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update password",
            )

        logger.info(f"Password reset completed for user {email}")
        return {"message": "Password has been successfully reset"}

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error during password reset confirmation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    current_user: UserInDB = Depends(get_current_user),
):
    """
    Refresh access token.

    Generates a new access token for the current user.
    """
    try:
        # Generate new token response
        token_response = generate_token_response(current_user.id, current_user.email)

        logger.info(f"Token refreshed for user {current_user.email}")
        return Token(**token_response)

    except Exception as e:
        logger.error(f"Error refreshing token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
