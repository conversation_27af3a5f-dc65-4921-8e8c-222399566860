"""
API endpoints for failed messages.

This module provides RESTful endpoints for managing failed messages
including error tracking, retry mechanisms, and dead letter queue management.
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from fastapi.responses import JSONResponse

from app.core.database import DatabaseManager, get_database
from app.core.exceptions import DatabaseError, NotFoundError, ValidationError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.failed_messages import (
    FailedMessageCreate,
    FailedMessageFilterParams,
    FailedMessageResponse,
    FailedMessageStatsResponse,
    FailedMessageUpdate,
)
from app.repositories.failed_messages import FailedMessageRepository

logger = get_logger(__name__)
router = APIRouter()


async def get_failed_message_repository(
    db: DatabaseManager = Depends(get_database),
) -> FailedMessageRepository:
    """Dependency to get failed message repository."""
    return FailedMessageRepository(db)


@router.post("/", response_model=FailedMessageResponse, status_code=201)
async def create_failed_message(
    message_data: FailedMessageCreate,
    repository: FailedMessageRepository = Depends(get_failed_message_repository),
):
    """Create a new failed message."""
    try:
        logger.info("Creating new failed message", message_id=message_data.message_id)

        # Check if message already exists
        existing_message = await repository.get_by_field(
            "message_id", message_data.message_id
        )
        if existing_message:
            raise ValidationError(
                f"Message with ID {message_data.message_id} already exists"
            )

        message = await repository.create(message_data)
        logger.info(
            "Failed message created successfully",
            message_id=message.message_id,
            id=message.id,
        )

        return message

    except ValidationError as e:
        logger.warning("Validation error creating failed message", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error creating failed message", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to create failed message")
    except Exception as e:
        logger.error(
            "Unexpected error creating failed message", error=str(e), exc_info=True
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics", response_model=FailedMessageStatsResponse)
async def get_failed_message_statistics(
    start_date: Optional[datetime] = Query(
        None, description="Start date for statistics"
    ),
    end_date: Optional[datetime] = Query(None, description="End date for statistics"),
    queue_name: Optional[str] = Query(None, description="Filter by queue name"),
    repository: FailedMessageRepository = Depends(get_failed_message_repository),
):
    """Get failed message statistics."""
    try:
        stats = await repository.get_failed_message_statistics(
            start_date, end_date, queue_name
        )
        logger.info("Retrieved failed message statistics")

        return stats
    except DatabaseError as e:
        logger.error("Database error getting failed message statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get failed message")
    except Exception as e:
        logger.error(
            "Unexpected error getting failed message statistics",
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{message_id}", response_model=FailedMessageResponse)
async def get_failed_message(
    message_id: str = Path(..., description="Message ID"),
    repository: FailedMessageRepository = Depends(get_failed_message_repository),
):
    """Get a failed message by ID."""
    try:
        message = await repository.get_by_field("message_id", message_id)
        if not message:
            raise NotFoundError("Failed Message", message_id)

        return message

    except NotFoundError as e:
        logger.warning("Failed message not found", message_id=message_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error getting failed message", message_id=message_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to get failed message")
    except Exception as e:
        logger.error(
            "Unexpected error getting failed message",
            message_id=message_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{message_id}", response_model=FailedMessageResponse)
async def update_failed_message(
    message_id: str = Path(..., description="Message ID"),
    message_data: FailedMessageUpdate = None,
    repository: FailedMessageRepository = Depends(get_failed_message_repository),
):
    """Update a failed message."""
    try:
        # Get existing message
        existing_message = await repository.get_by_field("message_id", message_id)
        if not existing_message:
            raise NotFoundError("Failed Message", message_id)

        # Update message
        updated_message = await repository.update(existing_message.id, message_data)
        logger.info("Failed message updated successfully", message_id=message_id)

        return updated_message

    except NotFoundError as e:
        logger.warning("Failed message not found for update", message_id=message_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error updating failed message",
            message_id=message_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to update failed message")
    except Exception as e:
        logger.error(
            "Unexpected error updating failed message",
            message_id=message_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{message_id}", status_code=204)
async def delete_failed_message(
    message_id: str = Path(..., description="Message ID"),
    repository: FailedMessageRepository = Depends(get_failed_message_repository),
):
    """Delete a failed message."""
    try:
        # Get existing message
        existing_message = await repository.get_by_field("message_id", message_id)
        if not existing_message:
            raise NotFoundError("Failed Message", message_id)

        # Delete message
        success = await repository.delete(existing_message.id)
        if not success:
            raise DatabaseError("Failed to delete failed message")

        logger.info("Failed message deleted successfully", message_id=message_id)
        return JSONResponse(status_code=204, content=None)

    except NotFoundError as e:
        logger.warning("Failed message not found for deletion", message_id=message_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error deleting failed message",
            message_id=message_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to delete failed message")
    except Exception as e:
        logger.error(
            "Unexpected error deleting failed message",
            message_id=message_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=PaginatedResponse)
async def list_failed_messages(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    queue_name: Optional[str] = Query(None, description="Filter by queue name"),
    error_type: Optional[str] = Query(None, description="Filter by error type"),
    retry_status: Optional[str] = Query(None, description="Filter by retry status"),
    start_date: Optional[datetime] = Query(
        None, description="Filter messages after this date"
    ),
    end_date: Optional[datetime] = Query(
        None, description="Filter messages before this date"
    ),
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: Optional[str] = Query("first_failed_at", description="Sort field"),
    sort_order: Optional[str] = Query(
        "desc", pattern="^(asc|desc)$", description="Sort order"
    ),
    repository: FailedMessageRepository = Depends(get_failed_message_repository),
):
    """List failed messages with filtering and pagination."""
    try:
        pagination = PaginationParams(page=page, size=size)
        filters = FailedMessageFilterParams(
            queue_name=queue_name,
            error_type=error_type,
            retry_status=retry_status,
            start_date=start_date,
            end_date=end_date,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
        )

        result = await repository.list_with_filters(pagination, filters)
        logger.info("Listed failed messages", total=result.total, page=page, size=size)

        return result

    except ValidationError as e:
        logger.warning("Validation error listing failed messages", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error listing failed messages", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list failed messages")
    except Exception as e:
        logger.error(
            "Unexpected error listing failed messages", error=str(e), exc_info=True
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/queue/{queue_name}", response_model=List[FailedMessageResponse])
async def get_messages_by_queue(
    queue_name: str = Path(..., description="Queue Name"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of messages to return"
    ),
    repository: FailedMessageRepository = Depends(get_failed_message_repository),
):
    """Get failed messages for a specific queue."""
    try:
        messages = await repository.get_by_field_multiple(
            "queue_name", queue_name, limit
        )
        logger.info(
            "Retrieved messages by queue", queue_name=queue_name, count=len(messages)
        )

        return messages

    except DatabaseError as e:
        logger.error(
            "Database error getting messages by queue",
            queue_name=queue_name,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to get messages by queue")
    except Exception as e:
        logger.error(
            "Unexpected error getting messages by queue",
            queue_name=queue_name,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{message_id}/retry")
async def retry_failed_message(
    message_id: str = Path(..., description="Message ID"),
    repository: FailedMessageRepository = Depends(get_failed_message_repository),
):
    """Retry a failed message."""
    try:
        # Get existing message
        existing_message = await repository.get_by_field("message_id", message_id)
        if not existing_message:
            raise NotFoundError("Failed Message", message_id)

        # Update retry count and status
        from app.models.failed_messages import FailedMessageUpdate

        update_data = FailedMessageUpdate(
            retry_count=existing_message.retry_count + 1, last_retry_at=datetime.now()
        )

        updated_message = await repository.update(existing_message.id, update_data)
        logger.info("Failed message retry initiated", message_id=message_id)

        return {
            "message": "Retry initiated",
            "message_id": message_id,
            "retry_count": updated_message.retry_count,
        }

    except NotFoundError as e:
        logger.warning("Failed message not found for retry", message_id=message_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error retrying failed message",
            message_id=message_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to retry failed message")
    except Exception as e:
        logger.error(
            "Unexpected error retrying failed message",
            message_id=message_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/bulk-retry")
async def bulk_retry_failed_messages(
    queue_name: Optional[str] = Query(None, description="Queue name to retry"),
    error_type: Optional[str] = Query(None, description="Error type to retry"),
    max_retry_count: int = Query(3, description="Maximum retry count filter"),
    repository: FailedMessageRepository = Depends(get_failed_message_repository),
):
    """Bulk retry failed messages based on criteria."""
    try:
        # This would typically involve more complex logic
        # For now, return a simple response
        logger.info(
            "Bulk retry initiated", queue_name=queue_name, error_type=error_type
        )

        return {
            "message": "Bulk retry initiated",
            "criteria": {
                "queue_name": queue_name,
                "error_type": error_type,
                "max_retry_count": max_retry_count,
            },
        }

    except DatabaseError as e:
        logger.error("Database error in bulk retry", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to perform bulk retry")
    except Exception as e:
        logger.error("Unexpected error in bulk retry", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
