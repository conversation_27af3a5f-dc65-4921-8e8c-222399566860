"""
API endpoints for inventory transactions.

This module provides RESTful endpoints for managing inventory transactions
including stock movements, adjustments, and inventory-specific operations.
"""

from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from fastapi.responses import JSONResponse

from app.core.database import DatabaseManager, get_database
from app.core.exceptions import DatabaseError, NotFoundError, ValidationError
from app.core.logging import get_logger
from app.models.base import PaginatedResponse, PaginationParams
from app.models.inventory_transactions import (
    InventoryStatsResponse,
    InventoryTransactionCreate,
    InventoryTransactionFilterParams,
    InventoryTransactionResponse,
    InventoryTransactionUpdate,
)
from app.repositories.inventory_transactions import InventoryTransactionRepository

logger = get_logger(__name__)
router = APIRouter()


async def get_inventory_transaction_repository(
    db: DatabaseManager = Depends(get_database),
) -> InventoryTransactionRepository:
    """Dependency to get inventory transaction repository."""
    return InventoryTransactionRepository(db)


@router.post("/", response_model=InventoryTransactionResponse, status_code=201)
async def create_inventory_transaction(
    transaction_data: InventoryTransactionCreate,
    repository: InventoryTransactionRepository = Depends(
        get_inventory_transaction_repository
    ),
):
    """Create a new inventory transaction."""
    try:
        logger.info(
            "Creating new inventory transaction",
            transaction_id=transaction_data.transaction_id,
        )

        # Check if transaction already exists
        existing_transaction = await repository.get_by_field(
            "transaction_id", transaction_data.transaction_id
        )
        if existing_transaction:
            raise ValidationError(
                f"Transaction with ID {transaction_data.transaction_id} already exists"
            )

        transaction = await repository.create(transaction_data)
        logger.info(
            "Inventory transaction created successfully",
            transaction_id=transaction.transaction_id,
            id=transaction.id,
        )

        return transaction

    except ValidationError as e:
        logger.warning("Validation error creating inventory transaction", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error creating inventory transaction", error=str(e))
        raise HTTPException(
            status_code=500, detail="Failed to create inventory transaction"
        )
    except Exception as e:
        logger.error(
            "Unexpected error creating inventory transaction",
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics", response_model=InventoryStatsResponse)
async def get_inventory_statistics(
    start_date: Optional[datetime] = Query(
        None, description="Start date for statistics"
    ),
    end_date: Optional[datetime] = Query(None, description="End date for statistics"),
    location: Optional[str] = Query(None, description="Filter by location"),
    repository: InventoryTransactionRepository = Depends(
        get_inventory_transaction_repository
    ),
):
    """Get inventory statistics."""
    try:
        stats = await repository.get_inventory_statistics(
            start_date, end_date, location
        )
        logger.info("Retrieved inventory statistics")

        return stats
    except DatabaseError as e:
        logger.error("Database error getting inventory statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Database error")
    except Exception as e:
        logger.error(
            "Unexpected error getting inventory statistics",
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{transaction_id}", response_model=InventoryTransactionResponse)
async def get_inventory_transaction(
    transaction_id: str = Path(..., description="Transaction ID"),
    repository: InventoryTransactionRepository = Depends(
        get_inventory_transaction_repository
    ),
):
    """Get an inventory transaction by ID."""
    try:
        transaction = await repository.get_by_field("transaction_id", transaction_id)
        if not transaction:
            raise NotFoundError("Inventory Transaction", transaction_id)

        return transaction

    except NotFoundError as e:
        logger.warning("Inventory transaction not found", transaction_id=transaction_id)
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error getting inventory transaction",
            transaction_id=transaction_id,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to get inventory transaction"
        )
    except Exception as e:
        logger.error(
            "Unexpected error getting inventory transaction",
            transaction_id=transaction_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{transaction_id}", response_model=InventoryTransactionResponse)
async def update_inventory_transaction(
    transaction_id: str = Path(..., description="Transaction ID"),
    transaction_data: InventoryTransactionUpdate = None,
    repository: InventoryTransactionRepository = Depends(
        get_inventory_transaction_repository
    ),
):
    """Update an inventory transaction."""
    try:
        # Get existing transaction
        existing_transaction = await repository.get_by_field(
            "transaction_id", transaction_id
        )
        if not existing_transaction:
            raise NotFoundError("Inventory Transaction", transaction_id)

        # Update transaction
        updated_transaction = await repository.update(
            existing_transaction.id, transaction_data
        )
        logger.info(
            "Inventory transaction updated successfully", transaction_id=transaction_id
        )

        return updated_transaction

    except NotFoundError as e:
        logger.warning(
            "Inventory transaction not found for update", transaction_id=transaction_id
        )
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error updating inventory transaction",
            transaction_id=transaction_id,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to update inventory transaction"
        )
    except Exception as e:
        logger.error(
            "Unexpected error updating inventory transaction",
            transaction_id=transaction_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{transaction_id}", status_code=204)
async def delete_inventory_transaction(
    transaction_id: str = Path(..., description="Transaction ID"),
    repository: InventoryTransactionRepository = Depends(
        get_inventory_transaction_repository
    ),
):
    """Delete an inventory transaction."""
    try:
        # Get existing transaction
        existing_transaction = await repository.get_by_field(
            "transaction_id", transaction_id
        )
        if not existing_transaction:
            raise NotFoundError("Inventory Transaction", transaction_id)

        # Delete transaction
        success = await repository.delete(existing_transaction.id)
        if not success:
            raise DatabaseError("Failed to delete inventory transaction")

        logger.info(
            "Inventory transaction deleted successfully", transaction_id=transaction_id
        )
        return JSONResponse(status_code=204, content=None)

    except NotFoundError as e:
        logger.warning(
            "Inventory transaction not found for deletion",
            transaction_id=transaction_id,
        )
        raise HTTPException(status_code=404, detail=str(e))
    except DatabaseError as e:
        logger.error(
            "Database error deleting inventory transaction",
            transaction_id=transaction_id,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to delete inventory transaction"
        )
    except Exception as e:
        logger.error(
            "Unexpected error deleting inventory transaction",
            transaction_id=transaction_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=PaginatedResponse)
async def list_inventory_transactions(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=1000, description="Page size"),
    item_sku: Optional[str] = Query(None, description="Filter by item SKU"),
    transaction_type: Optional[str] = Query(
        None, description="Filter by transaction type"
    ),
    location: Optional[str] = Query(None, description="Filter by location"),
    operator_id: Optional[str] = Query(None, description="Filter by operator ID"),
    start_date: Optional[datetime] = Query(
        None, description="Filter transactions after this date"
    ),
    end_date: Optional[datetime] = Query(
        None, description="Filter transactions before this date"
    ),
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: Optional[str] = Query("transaction_time", description="Sort field"),
    sort_order: Optional[str] = Query(
        "desc", pattern="^(asc|desc)$", description="Sort order"
    ),
    repository: InventoryTransactionRepository = Depends(
        get_inventory_transaction_repository
    ),
):
    """List inventory transactions with filtering and pagination."""
    try:
        pagination = PaginationParams(page=page, size=size)
        filters = InventoryTransactionFilterParams(
            item_sku=item_sku,
            transaction_type=transaction_type,
            location=location,
            operator_id=operator_id,
            start_date=start_date,
            end_date=end_date,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
        )

        result = await repository.list_with_filters(pagination, filters)
        logger.info(
            "Listed inventory transactions", total=result.total, page=page, size=size
        )

        return result

    except ValidationError as e:
        logger.warning("Validation error listing inventory transactions", error=str(e))
        raise HTTPException(status_code=422, detail=str(e))
    except DatabaseError as e:
        logger.error("Database error listing inventory transactions", error=str(e))
        raise HTTPException(
            status_code=500, detail="Failed to list inventory transactions"
        )
    except Exception as e:
        logger.error(
            "Unexpected error listing inventory transactions",
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/item/{item_sku}", response_model=List[InventoryTransactionResponse])
async def get_transactions_by_item(
    item_sku: str = Path(..., description="Item SKU"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of transactions to return"
    ),
    repository: InventoryTransactionRepository = Depends(
        get_inventory_transaction_repository
    ),
):
    """Get transactions for a specific item."""
    try:
        transactions = await repository.get_by_field_multiple(
            "item_sku", item_sku, limit
        )
        logger.info(
            "Retrieved transactions by item", item_sku=item_sku, count=len(transactions)
        )

        return transactions

    except DatabaseError as e:
        logger.error(
            "Database error getting transactions by item",
            item_sku=item_sku,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to get transactions by item"
        )
    except Exception as e:
        logger.error(
            "Unexpected error getting transactions by item",
            item_sku=item_sku,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/location/{location}", response_model=List[InventoryTransactionResponse])
async def get_transactions_by_location(
    location: str = Path(..., description="Location"),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of transactions to return"
    ),
    repository: InventoryTransactionRepository = Depends(
        get_inventory_transaction_repository
    ),
):
    """Get transactions for a specific location."""
    try:
        transactions = await repository.get_by_field_multiple(
            "location", location, limit
        )
        logger.info(
            "Retrieved transactions by location",
            location=location,
            count=len(transactions),
        )

        return transactions

    except DatabaseError as e:
        logger.error(
            "Database error getting transactions by location",
            location=location,
            error=str(e),
        )
        raise HTTPException(
            status_code=500, detail="Failed to get transactions by location"
        )
    except Exception as e:
        logger.error(
            "Unexpected error getting transactions by location",
            location=location,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail="Internal server error")
