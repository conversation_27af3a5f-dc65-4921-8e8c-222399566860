"""
FastAPI dependency injection setup.

This module provides dependency injection for services, repositories,
and other components used throughout the API.
"""

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from app.core.auth import verify_token
from app.core.database import DatabaseManager, get_database
from app.models.users import UserInDB
from app.repositories.failed_messages import FailedMessageRepository
from app.repositories.inventory_transactions import InventoryTransactionRepository
from app.repositories.operator_events import OperatorEventRepository
from app.repositories.picks import PickRepository
from app.repositories.shipments import ShipmentRepository
from app.repositories.users import UserRepository
from app.repositories.vehicle_movements import VehicleMovementRepository
from app.services.picks import PickService

# Security scheme
security = HTTPBearer()


# Repository dependencies
async def get_pick_repository(
    db: DatabaseManager = Depends(get_database),
) -> PickRepository:
    """Get pick repository dependency."""
    return PickRepository(db)


async def get_vehicle_movement_repository(
    db: DatabaseManager = Depends(get_database),
) -> VehicleMovementRepository:
    """Get vehicle movement repository dependency."""
    return VehicleMovementRepository(db)


async def get_operator_event_repository(
    db: DatabaseManager = Depends(get_database),
) -> OperatorEventRepository:
    """Get operator event repository dependency."""
    return OperatorEventRepository(db)


async def get_inventory_transaction_repository(
    db: DatabaseManager = Depends(get_database),
) -> InventoryTransactionRepository:
    """Get inventory transaction repository dependency."""
    return InventoryTransactionRepository(db)


async def get_shipment_repository(
    db: DatabaseManager = Depends(get_database),
) -> ShipmentRepository:
    """Get shipment repository dependency."""
    return ShipmentRepository(db)


async def get_failed_message_repository(
    db: DatabaseManager = Depends(get_database),
) -> FailedMessageRepository:
    """Get failed message repository dependency."""
    return FailedMessageRepository(db)


async def get_user_repository(
    db: DatabaseManager = Depends(get_database),
) -> UserRepository:
    """Get user repository dependency."""
    return UserRepository(db)


# Authentication dependencies
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    user_repository: UserRepository = Depends(get_user_repository),
) -> UserInDB:
    """Get current authenticated user."""
    token = credentials.credentials
    token_data = verify_token(token)

    if token_data is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user = await user_repository.get_user_by_email(token_data.email)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


async def get_current_active_user(
    current_user: UserInDB = Depends(get_current_user),
) -> UserInDB:
    """Get current active user (alias for get_current_user)."""
    return current_user


# Service dependencies
async def get_pick_service(
    pick_repository: PickRepository = Depends(get_pick_repository),
) -> PickService:
    """Get pick service dependency."""
    return PickService(pick_repository)
