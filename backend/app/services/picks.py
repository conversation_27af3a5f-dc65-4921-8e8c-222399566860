"""
Service layer for pick operations.

This module contains business logic for pick operations including
validation, workflow management, and coordination with other services.
"""

import logging
from datetime import datetime
from typing import List, Optional

from app.core.exceptions import BusinessLogicError, ValidationError
from app.core.logging import get_logger
from app.models.picks import <PERSON><PERSON><PERSON>, PickUp<PERSON>, PickResponse, PickStatus
from app.repositories.picks import PickRepository

logger = get_logger(__name__)


class PickService:
    """Service class for pick operations business logic."""
    
    def __init__(self, pick_repository: PickRepository):
        self.pick_repository = pick_repository
    
    async def create_pick(self, pick_data: PickCreate) -> PickResponse:
        """Create a new pick with business logic validation."""
        logger.info("Creating pick with business validation", pick_id=pick_data.pick_id)
        
        # Business logic validations
        await self._validate_pick_creation(pick_data)
        
        # Create the pick
        pick = await self.pick_repository.create(pick_data)
        
        logger.info("Pick created successfully", pick_id=pick.pick_id)
        return pick
    
    async def start_pick(self, pick_id: str, operator_id: str) -> PickResponse:
        """Start a pick operation."""
        logger.info("Starting pick", pick_id=pick_id, operator_id=operator_id)
        
        # Get existing pick
        pick = await self.pick_repository.get_by_pick_id(pick_id)
        if not pick:
            raise ValidationError(f"Pick {pick_id} not found")
        
        # Validate pick can be started
        if pick.pick_status != PickStatus.PENDING:
            raise BusinessLogicError(f"Pick {pick_id} cannot be started - current status: {pick.pick_status}")
        
        if pick.operator_id != operator_id:
            raise BusinessLogicError(f"Pick {pick_id} is assigned to different operator")
        
        # Update pick status
        updated_pick = await self.pick_repository.update_pick_status(
            pick_id, PickStatus.IN_PROGRESS.value
        )
        
        logger.info("Pick started successfully", pick_id=pick_id)
        return updated_pick
    
    async def complete_pick(self, pick_id: str, quantity_picked: int) -> PickResponse:
        """Complete a pick operation."""
        logger.info("Completing pick", pick_id=pick_id, quantity_picked=quantity_picked)
        
        # Get existing pick
        pick = await self.pick_repository.get_by_pick_id(pick_id)
        if not pick:
            raise ValidationError(f"Pick {pick_id} not found")
        
        # Validate pick can be completed
        if pick.pick_status != PickStatus.IN_PROGRESS:
            raise BusinessLogicError(f"Pick {pick_id} cannot be completed - current status: {pick.pick_status}")
        
        # Determine final status based on quantity picked
        if quantity_picked == pick.quantity_requested:
            final_status = PickStatus.COMPLETED
        elif quantity_picked < pick.quantity_requested:
            final_status = PickStatus.SHORT_PICKED
        else:
            raise BusinessLogicError(f"Picked quantity ({quantity_picked}) cannot exceed requested quantity ({pick.quantity_requested})")
        
        # Update pick with completion details
        end_time = datetime.utcnow()
        updated_pick = await self.pick_repository.update_pick_status(
            pick_id, final_status.value, end_time
        )
        
        # Update quantity picked
        if quantity_picked != pick.quantity_picked:
            update_data = PickUpdate(quantity_picked=quantity_picked)
            updated_pick = await self.pick_repository.update(updated_pick.id, update_data)
        
        logger.info("Pick completed successfully", pick_id=pick_id, status=final_status.value)
        return updated_pick
    
    async def cancel_pick(self, pick_id: str, reason: str) -> PickResponse:
        """Cancel a pick operation."""
        logger.info("Cancelling pick", pick_id=pick_id, reason=reason)
        
        # Get existing pick
        pick = await self.pick_repository.get_by_pick_id(pick_id)
        if not pick:
            raise ValidationError(f"Pick {pick_id} not found")
        
        # Validate pick can be cancelled
        if pick.pick_status in [PickStatus.COMPLETED, PickStatus.CANCELLED]:
            raise BusinessLogicError(f"Pick {pick_id} cannot be cancelled - current status: {pick.pick_status}")
        
        # Update pick status
        updated_pick = await self.pick_repository.update_pick_status(
            pick_id, PickStatus.CANCELLED.value, datetime.utcnow()
        )
        
        logger.info("Pick cancelled successfully", pick_id=pick_id, reason=reason)
        return updated_pick
    
    async def get_operator_active_picks(self, operator_id: str) -> List[PickResponse]:
        """Get active picks for an operator."""
        logger.info("Getting active picks for operator", operator_id=operator_id)
        
        # Get all picks for operator
        all_picks = await self.pick_repository.get_picks_by_operator(operator_id, 1000)
        
        # Filter for active picks (pending or in progress)
        active_picks = [
            pick for pick in all_picks 
            if pick.pick_status in [PickStatus.PENDING, PickStatus.IN_PROGRESS]
        ]
        
        logger.info("Retrieved active picks", operator_id=operator_id, count=len(active_picks))
        return active_picks
    
    async def _validate_pick_creation(self, pick_data: PickCreate) -> None:
        """Validate pick creation business rules."""
        
        # Validate pick ID uniqueness
        existing_pick = await self.pick_repository.get_by_pick_id(pick_data.pick_id)
        if existing_pick:
            raise ValidationError(f"Pick with ID {pick_data.pick_id} already exists")
        
        # Validate quantity
        if pick_data.quantity_requested <= 0:
            raise ValidationError("Quantity requested must be positive")
        
        if pick_data.quantity_picked > pick_data.quantity_requested:
            raise ValidationError("Quantity picked cannot exceed quantity requested")
        
        # Validate location format (basic validation)
        if not pick_data.location_from or len(pick_data.location_from.split('-')) != 3:
            raise ValidationError("Invalid location format")
        
        # Additional business validations can be added here
        # e.g., validate operator exists, item exists, location exists, etc.
        
        logger.debug("Pick creation validation passed", pick_id=pick_data.pick_id)
