"""
Pydantic models for failed messages.

This module contains all schemas related to failed message processing,
error tracking, and dead letter queue management.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import Field, validator

from app.models.base import BaseSchema, FilterParams


class FailedMessageBase(BaseSchema):
    """Base failed message schema with common fields."""
    
    message_id: UUID = Field(..., description="Unique message identifier")
    queue_name: str = Field(..., min_length=1, max_length=100, description="Name of the queue where message failed")
    event_type: Optional[str] = Field(None, max_length=50, description="Type of event that failed processing")
    message_body: Dict[str, Any] = Field(..., description="Original message content as JSON")
    error_message: Optional[str] = Field(None, description="Error message describing the failure")
    retry_count: int = Field(0, ge=0, description="Number of retry attempts")
    first_failed_at: datetime = Field(..., description="When message first failed")
    last_retry_at: Optional[datetime] = Field(None, description="Last retry attempt timestamp")
    resolved_at: Optional[datetime] = Field(None, description="When message was successfully resolved")
    
    @validator("last_retry_at")
    def validate_last_retry_at(cls, v, values):
        """Validate that last retry time is after first failed time."""
        if v and "first_failed_at" in values and v < values["first_failed_at"]:
            raise ValueError("Last retry time must be after first failed time")
        return v
    
    @validator("resolved_at")
    def validate_resolved_at(cls, v, values):
        """Validate that resolved time is after first failed time."""
        if v and "first_failed_at" in values and v < values["first_failed_at"]:
            raise ValueError("Resolved time must be after first failed time")
        return v
    
    @validator("retry_count")
    def validate_retry_count_consistency(cls, v, values):
        """Validate retry count consistency with retry timestamp."""
        if v > 0 and "last_retry_at" not in values:
            # This validation might be too strict for creation, so we'll make it a warning
            pass
        return v


class FailedMessageCreate(FailedMessageBase):
    """Schema for creating a new failed message."""
    
    # Override fields that should not be provided during creation
    message_id: Optional[UUID] = Field(None, description="Unique message identifier (auto-generated)")
    retry_count: int = Field(0, description="Number of retry attempts")
    last_retry_at: Optional[datetime] = Field(None, description="Last retry attempt timestamp")
    resolved_at: Optional[datetime] = Field(None, description="When message was successfully resolved")


class FailedMessageUpdate(BaseSchema):
    """Schema for updating an existing failed message."""
    
    error_message: Optional[str] = Field(None, description="Error message describing the failure")
    retry_count: Optional[int] = Field(None, ge=0, description="Number of retry attempts")
    last_retry_at: Optional[datetime] = Field(None, description="Last retry attempt timestamp")
    resolved_at: Optional[datetime] = Field(None, description="When message was successfully resolved")


class FailedMessageResponse(FailedMessageBase):
    """Schema for failed message response."""
    
    id: int = Field(..., description="Database record ID")
    created_at: datetime = Field(..., description="Record creation timestamp")


class FailedMessageListResponse(BaseSchema):
    """Schema for failed message list response with pagination."""
    
    messages: List[FailedMessageResponse] = Field(..., description="List of failed messages")
    total: int = Field(..., description="Total number of failed messages")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")


class FailedMessageFilterParams(FilterParams):
    """Filter parameters for failed message queries."""
    
    queue_name: Optional[str] = Field(None, description="Filter by queue name")
    event_type: Optional[str] = Field(None, description="Filter by event type")
    resolved: Optional[bool] = Field(None, description="Filter by resolution status")
    min_retry_count: Optional[int] = Field(None, ge=0, description="Minimum retry count")
    max_retry_count: Optional[int] = Field(None, ge=0, description="Maximum retry count")
    start_date: Optional[datetime] = Field(None, description="Filter messages after this date")
    end_date: Optional[datetime] = Field(None, description="Filter messages before this date")
    
    @validator("end_date")
    def validate_date_range(cls, v, values):
        """Validate that end date is after start date."""
        if v and "start_date" in values and values["start_date"] and v < values["start_date"]:
            raise ValueError("End date must be after start date")
        return v
    
    @validator("max_retry_count")
    def validate_retry_count_range(cls, v, values):
        """Validate that max retry count is greater than min retry count."""
        if v and "min_retry_count" in values and values["min_retry_count"] and v < values["min_retry_count"]:
            raise ValueError("Maximum retry count must be greater than minimum retry count")
        return v


class FailedMessageStatsResponse(BaseSchema):
    """Schema for failed message statistics response."""
    
    total_failed_messages: int = Field(..., description="Total number of failed messages")
    unresolved_messages: int = Field(..., description="Number of unresolved messages")
    resolved_messages: int = Field(..., description="Number of resolved messages")
    average_retry_count: float = Field(..., description="Average number of retries")
    by_queue: dict = Field(default_factory=dict, description="Statistics by queue name")
    by_event_type: dict = Field(default_factory=dict, description="Statistics by event type")
    recent_failures: List[Dict[str, Any]] = Field(default_factory=list, description="Recent failure patterns")
    resolution_rate: float = Field(..., description="Message resolution rate percentage")


class RetryRequest(BaseSchema):
    """Schema for retry request."""
    
    message_ids: List[int] = Field(..., min_items=1, description="List of message IDs to retry")
    max_retries: Optional[int] = Field(None, ge=1, le=10, description="Maximum number of retry attempts")


class RetryResponse(BaseSchema):
    """Schema for retry operation response."""
    
    total_requested: int = Field(..., description="Total number of messages requested for retry")
    successfully_queued: int = Field(..., description="Number of messages successfully queued for retry")
    failed_to_queue: int = Field(..., description="Number of messages that failed to queue")
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="List of errors encountered")


class BulkResolveRequest(BaseSchema):
    """Schema for bulk resolve request."""
    
    message_ids: List[int] = Field(..., min_items=1, description="List of message IDs to mark as resolved")
    resolution_note: Optional[str] = Field(None, description="Note about the resolution")


class BulkResolveResponse(BaseSchema):
    """Schema for bulk resolve operation response."""
    
    total_requested: int = Field(..., description="Total number of messages requested for resolution")
    successfully_resolved: int = Field(..., description="Number of messages successfully resolved")
    failed_to_resolve: int = Field(..., description="Number of messages that failed to resolve")
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="List of errors encountered")


class MessageAnalysisResponse(BaseSchema):
    """Schema for message failure analysis response."""
    
    message_id: int = Field(..., description="Message ID")
    failure_pattern: str = Field(..., description="Identified failure pattern")
    suggested_action: str = Field(..., description="Suggested remediation action")
    similar_failures: List[int] = Field(default_factory=list, description="IDs of similar failed messages")
    root_cause: Optional[str] = Field(None, description="Identified root cause")
    priority: str = Field(..., description="Priority level for resolution")


class QueueHealthResponse(BaseSchema):
    """Schema for queue health monitoring response."""
    
    queue_name: str = Field(..., description="Queue name")
    total_messages: int = Field(..., description="Total messages processed")
    failed_messages: int = Field(..., description="Number of failed messages")
    failure_rate: float = Field(..., description="Failure rate percentage")
    average_processing_time: Optional[float] = Field(None, description="Average processing time in seconds")
    recent_error_patterns: List[str] = Field(default_factory=list, description="Recent error patterns")
    health_status: str = Field(..., description="Overall health status")
    recommendations: List[str] = Field(default_factory=list, description="Health improvement recommendations")
