"""
Pydantic models for inventory transactions.

This module contains all schemas related to stock movements, inventory adjustments,
and cycle counting activities.
"""

from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import Field, validator

from app.models.base import BaseSchema, FilterParams, TimestampMixin


class TransactionType(str, Enum):
    """Transaction type enumeration."""

    RECEIPT = "receipt"
    PICK = "pick"
    PUTAWAY = "putaway"
    ADJUSTMENT = "adjustment"
    CYCLE_COUNT = "cycle_count"
    TRANSFER = "transfer"
    DAMAGE = "damage"
    RETURN = "return"


class UnitOfMeasure(str, Enum):
    """Unit of measure enumeration."""

    EACH = "each"
    CASE = "case"
    PALLET = "pallet"
    KG = "kg"
    LBS = "lbs"
    LITER = "liter"
    GALLON = "gallon"


class QualityStatus(str, Enum):
    """Quality status enumeration."""

    GOOD = "good"
    DAMAGED = "damaged"
    EXPIRED = "expired"
    QUARANTINE = "quarantine"


class InventoryTransactionBase(BaseSchema):
    """Base inventory transaction schema with common fields."""

    transaction_id: str = Field(
        ...,
        description="Unique transaction identifier",
        pattern=r"^TXN-\d{8}-[A-Z0-9]{6}$",
    )
    item_sku: str = Field(
        ..., description="Stock keeping unit", pattern=r"^SKU-[A-Z0-9]{8}$"
    )
    item_description: Optional[str] = Field(None, description="Item description")
    transaction_type: TransactionType = Field(..., description="Type of transaction")
    quantity_before: int = Field(..., description="Quantity before transaction")
    quantity_change: int = Field(
        ..., description="Quantity change (positive or negative)"
    )
    quantity_after: int = Field(..., description="Quantity after transaction")
    unit_of_measure: UnitOfMeasure = Field(..., description="Unit of measure")
    location: str = Field(
        ..., description="Inventory location", pattern=r"^[A-Z]-\d{2}-\d{2}$"
    )
    zone: Optional[str] = Field(None, description="Warehouse zone")
    lot_number: Optional[str] = Field(
        None, description="Lot or batch number", pattern=r"^LOT-[A-Z0-9]{8}$"
    )
    expiry_date: Optional[date] = Field(None, description="Item expiry date")
    cost_per_unit: Optional[Decimal] = Field(None, ge=0, description="Cost per unit")
    total_value: Optional[Decimal] = Field(None, description="Total transaction value")
    operator_id: str = Field(
        ..., description="Operator performing transaction", pattern=r"^OP-[A-Z0-9]{6}$"
    )
    reason_code: Optional[str] = Field(None, description="Reason for transaction")
    reference_document: Optional[str] = Field(
        None, description="Reference document number"
    )
    supplier_id: Optional[str] = Field(None, description="Supplier identifier")
    customer_id: Optional[str] = Field(None, description="Customer identifier")
    quality_status: Optional[QualityStatus] = Field(None, description="Quality status")
    storage_conditions: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Storage conditions"
    )
    cycle_count_info: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Cycle count data"
    )
    transaction_time: datetime = Field(..., description="When transaction occurred")

    @validator("quantity_after")
    def validate_quantity_after(cls, v, values):
        """Validate that quantity_after = quantity_before + quantity_change."""
        if "quantity_before" in values and "quantity_change" in values:
            expected = values["quantity_before"] + values["quantity_change"]
            if v != expected:
                raise ValueError(
                    f"Quantity after ({v}) must equal quantity before ({values['quantity_before']}) plus quantity change ({values['quantity_change']})"
                )
        return v

    @validator("total_value")
    def calculate_total_value(cls, v, values):
        """Calculate total value if not provided."""
        if v is None and "cost_per_unit" in values and "quantity_change" in values:
            if values["cost_per_unit"] is not None:
                return abs(values["quantity_change"]) * values["cost_per_unit"]
        return v

    @validator("expiry_date")
    def validate_expiry_date(cls, v):
        """Validate that expiry date is not in the past for new items."""
        if v and v < date.today():
            # Allow past dates for existing inventory adjustments
            pass
        return v

    @validator("storage_conditions")
    def validate_storage_conditions(cls, v):
        """Validate storage conditions structure."""
        if v:
            allowed_keys = {"temperature", "humidity", "special_requirements"}
            if not all(key in allowed_keys for key in v.keys()):
                raise ValueError(f"Storage conditions can only contain: {allowed_keys}")
        return v


class InventoryTransactionCreate(InventoryTransactionBase):
    """Schema for creating a new inventory transaction."""

    # Override calculated fields that should not be provided during creation
    quantity_after: Optional[int] = Field(
        None, description="Quantity after transaction (calculated)"
    )
    total_value: Optional[Decimal] = Field(
        None, description="Total transaction value (calculated)"
    )


class InventoryTransactionUpdate(BaseSchema):
    """Schema for updating an existing inventory transaction."""

    reason_code: Optional[str] = Field(None, description="Reason for transaction")
    reference_document: Optional[str] = Field(
        None, description="Reference document number"
    )
    quality_status: Optional[QualityStatus] = Field(None, description="Quality status")
    storage_conditions: Optional[Dict[str, Any]] = Field(
        None, description="Storage conditions"
    )
    cycle_count_info: Optional[Dict[str, Any]] = Field(
        None, description="Cycle count data"
    )


class InventoryTransactionResponse(InventoryTransactionBase, TimestampMixin):
    """Schema for inventory transaction response."""

    id: int = Field(..., description="Database record ID")


class InventoryTransactionListResponse(BaseSchema):
    """Schema for inventory transaction list response with pagination."""

    transactions: List[InventoryTransactionResponse] = Field(
        ..., description="List of inventory transactions"
    )
    total: int = Field(..., description="Total number of transactions")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")


class InventoryTransactionFilterParams(FilterParams):
    """Filter parameters for inventory transaction queries."""

    item_sku: Optional[str] = Field(None, description="Filter by item SKU")
    transaction_type: Optional[TransactionType] = Field(
        None, description="Filter by transaction type"
    )
    operator_id: Optional[str] = Field(None, description="Filter by operator ID")
    location: Optional[str] = Field(None, description="Filter by location")
    zone: Optional[str] = Field(None, description="Filter by warehouse zone")
    lot_number: Optional[str] = Field(None, description="Filter by lot number")
    quality_status: Optional[QualityStatus] = Field(
        None, description="Filter by quality status"
    )
    supplier_id: Optional[str] = Field(None, description="Filter by supplier ID")
    customer_id: Optional[str] = Field(None, description="Filter by customer ID")
    start_date: Optional[datetime] = Field(
        None, description="Filter transactions after this date"
    )
    end_date: Optional[datetime] = Field(
        None, description="Filter transactions before this date"
    )
    min_value: Optional[Decimal] = Field(
        None, ge=0, description="Minimum transaction value"
    )
    max_value: Optional[Decimal] = Field(
        None, ge=0, description="Maximum transaction value"
    )

    @validator("end_date")
    def validate_date_range(cls, v, values):
        """Validate that end date is after start date."""
        if (
            v
            and "start_date" in values
            and values["start_date"]
            and v < values["start_date"]
        ):
            raise ValueError("End date must be after start date")
        return v

    @validator("max_value")
    def validate_value_range(cls, v, values):
        """Validate that max value is greater than min value."""
        if (
            v
            and "min_value" in values
            and values["min_value"]
            and v < values["min_value"]
        ):
            raise ValueError("Maximum value must be greater than minimum value")
        return v


class InventoryStatsResponse(BaseSchema):
    """Schema for inventory statistics response."""

    total_transactions: int = Field(..., description="Total number of transactions")
    total_value: Decimal = Field(..., description="Total transaction value")
    by_transaction_type: dict = Field(
        default_factory=dict, description="Statistics by transaction type"
    )
    by_location: dict = Field(
        default_factory=dict, description="Statistics by location"
    )
    by_quality_status: dict = Field(
        default_factory=dict, description="Statistics by quality status"
    )
    top_items: list = Field(
        default_factory=list, description="Top items by transaction volume"
    )
    inventory_turnover: Optional[Decimal] = Field(
        None, description="Inventory turnover rate"
    )
    accuracy_rate: Optional[Decimal] = Field(
        None, description="Inventory accuracy rate"
    )


class CurrentInventoryResponse(BaseSchema):
    """Schema for current inventory levels response."""

    item_sku: str = Field(..., description="Stock keeping unit")
    item_description: Optional[str] = Field(None, description="Item description")
    current_quantity: int = Field(..., description="Current quantity on hand")
    unit_of_measure: UnitOfMeasure = Field(..., description="Unit of measure")
    locations: list = Field(
        default_factory=list, description="Locations where item is stored"
    )
    total_value: Optional[Decimal] = Field(None, description="Total inventory value")
    last_transaction_date: Optional[datetime] = Field(
        None, description="Last transaction date"
    )
    quality_breakdown: dict = Field(
        default_factory=dict, description="Quantity by quality status"
    )
