"""
Pydantic models for shipments.

This module contains all schemas related to order shipment processing,
packaging, and delivery information.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import Field, validator

from app.models.base import BaseSchema, TimestampMixin, FilterParams


class ShipmentType(str, Enum):
    """Shipment type enumeration."""
    STANDARD = "standard"
    EXPRESS = "express"
    OVERNIGHT = "overnight"
    SAME_DAY = "same_day"
    FREIGHT = "freight"
    LTL = "ltl"  # Less Than Truckload
    FTL = "ftl"  # Full Truckload


class ShipmentStatus(str, Enum):
    """Shipment status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    PACKED = "packed"
    SHIPPED = "shipped"
    IN_TRANSIT = "in_transit"
    DELIVERED = "delivered"
    CANCELLED = "cancelled"


class ServiceLevel(str, Enum):
    """Service level enumeration."""
    GROUND = "ground"
    AIR = "air"
    EXPRESS = "express"
    OVERNIGHT = "overnight"


class PackageInfo(BaseSchema):
    """Schema for package information."""
    
    package_id: str = Field(..., description="Package identifier")
    weight_kg: Decimal = Field(..., ge=0, description="Package weight in kilograms")
    dimensions: Dict[str, Decimal] = Field(..., description="Package dimensions (length, width, height)")
    tracking_number: Optional[str] = Field(None, description="Package tracking number")
    
    @validator("dimensions")
    def validate_dimensions(cls, v):
        """Validate package dimensions."""
        required_keys = {"length", "width", "height"}
        if not all(key in v for key in required_keys):
            raise ValueError(f"Dimensions must contain: {required_keys}")
        
        for key, value in v.items():
            if key in required_keys and value <= 0:
                raise ValueError(f"Dimension '{key}' must be positive")
        
        return v


class Address(BaseSchema):
    """Schema for address information."""
    
    street: str = Field(..., min_length=1, description="Street address")
    city: str = Field(..., min_length=1, description="City")
    state: str = Field(..., min_length=2, max_length=3, description="State/Province")
    postal_code: str = Field(..., min_length=3, description="Postal/ZIP code")
    country: str = Field(..., min_length=2, max_length=3, description="Country code")
    company: Optional[str] = Field(None, description="Company name")
    contact_name: Optional[str] = Field(None, description="Contact person name")
    phone: Optional[str] = Field(None, description="Phone number")


class HazmatInfo(BaseSchema):
    """Schema for hazardous material information."""
    
    un_number: str = Field(..., description="UN identification number")
    proper_shipping_name: str = Field(..., description="Proper shipping name")
    hazard_class: str = Field(..., description="Hazard class")
    packing_group: Optional[str] = Field(None, description="Packing group")
    special_provisions: Optional[List[str]] = Field(default_factory=list, description="Special provisions")


class ShipmentBase(BaseSchema):
    """Base shipment schema with common fields."""
    
    shipment_id: str = Field(..., description="Unique shipment identifier", pattern=r"^SHIP-\d{8}-[A-Z0-9]{6}$")
    order_id: str = Field(..., description="Associated order identifier", pattern=r"^ORD-\d{8}-[A-Z0-9]{4}$")
    customer_id: str = Field(..., description="Customer identifier", pattern=r"^CUST-[A-Z0-9]{8}$")
    shipment_type: ShipmentType = Field(..., description="Type of shipment")
    shipment_status: ShipmentStatus = Field(..., description="Shipment status")
    carrier: str = Field(..., min_length=1, description="Shipping carrier name")
    tracking_number: Optional[str] = Field(None, description="Carrier tracking number")
    service_level: Optional[ServiceLevel] = Field(None, description="Service level")
    total_weight_kg: Decimal = Field(..., ge=0, description="Total shipment weight in kilograms")
    total_volume_m3: Optional[Decimal] = Field(None, ge=0, description="Total shipment volume in cubic meters")
    package_count: int = Field(..., ge=1, description="Number of packages in shipment")
    packages: List[PackageInfo] = Field(..., description="Package details")
    shipping_address: Address = Field(..., description="Shipping address")
    billing_address: Address = Field(..., description="Billing address")
    shipping_cost: Optional[Decimal] = Field(None, ge=0, description="Shipping cost")
    insurance_value: Optional[Decimal] = Field(None, ge=0, description="Insurance value")
    special_instructions: Optional[str] = Field(None, description="Special handling instructions")
    hazmat_info: Optional[HazmatInfo] = Field(None, description="Hazardous material information")
    operator_id: str = Field(..., description="Operator processing shipment", pattern=r"^OP-[A-Z0-9]{6}$")
    dock_door: Optional[str] = Field(None, description="Dock door assignment")
    estimated_delivery: Optional[datetime] = Field(None, description="Estimated delivery timestamp")
    actual_delivery: Optional[datetime] = Field(None, description="Actual delivery timestamp")
    created_time: datetime = Field(..., description="Shipment creation time")
    shipped_time: Optional[datetime] = Field(None, description="When shipment was dispatched")
    
    @validator("packages")
    def validate_packages(cls, v, values):
        """Validate package information consistency."""
        if "package_count" in values and len(v) != values["package_count"]:
            raise ValueError("Number of packages must match package_count")
        
        # Validate total weight
        if "total_weight_kg" in values:
            total_package_weight = sum(pkg.weight_kg for pkg in v)
            if abs(total_package_weight - values["total_weight_kg"]) > 0.01:  # Allow small rounding differences
                raise ValueError("Total weight must equal sum of package weights")
        
        return v
    
    @validator("actual_delivery")
    def validate_delivery_times(cls, v, values):
        """Validate delivery time consistency."""
        if v:
            if "shipped_time" in values and values["shipped_time"] and v < values["shipped_time"]:
                raise ValueError("Actual delivery time must be after shipped time")
            
            if "created_time" in values and v < values["created_time"]:
                raise ValueError("Actual delivery time must be after creation time")
        
        return v
    
    @validator("shipped_time")
    def validate_shipped_time(cls, v, values):
        """Validate shipped time."""
        if v and "created_time" in values and v < values["created_time"]:
            raise ValueError("Shipped time must be after creation time")
        return v


class ShipmentCreate(ShipmentBase):
    """Schema for creating a new shipment."""
    
    # Override fields that should not be provided during creation
    shipment_status: ShipmentStatus = Field(ShipmentStatus.PENDING, description="Shipment status")
    tracking_number: Optional[str] = Field(None, description="Carrier tracking number")
    actual_delivery: Optional[datetime] = Field(None, description="Actual delivery timestamp")
    shipped_time: Optional[datetime] = Field(None, description="When shipment was dispatched")


class ShipmentUpdate(BaseSchema):
    """Schema for updating an existing shipment."""
    
    shipment_status: Optional[ShipmentStatus] = Field(None, description="Shipment status")
    tracking_number: Optional[str] = Field(None, description="Carrier tracking number")
    shipping_cost: Optional[Decimal] = Field(None, ge=0, description="Shipping cost")
    special_instructions: Optional[str] = Field(None, description="Special handling instructions")
    dock_door: Optional[str] = Field(None, description="Dock door assignment")
    estimated_delivery: Optional[datetime] = Field(None, description="Estimated delivery timestamp")
    actual_delivery: Optional[datetime] = Field(None, description="Actual delivery timestamp")
    shipped_time: Optional[datetime] = Field(None, description="When shipment was dispatched")


class ShipmentResponse(ShipmentBase, TimestampMixin):
    """Schema for shipment response."""
    
    id: int = Field(..., description="Database record ID")


class ShipmentListResponse(BaseSchema):
    """Schema for shipment list response with pagination."""
    
    shipments: List[ShipmentResponse] = Field(..., description="List of shipments")
    total: int = Field(..., description="Total number of shipments")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")


class ShipmentFilterParams(FilterParams):
    """Filter parameters for shipment queries."""
    
    order_id: Optional[str] = Field(None, description="Filter by order ID")
    customer_id: Optional[str] = Field(None, description="Filter by customer ID")
    shipment_type: Optional[ShipmentType] = Field(None, description="Filter by shipment type")
    shipment_status: Optional[ShipmentStatus] = Field(None, description="Filter by shipment status")
    carrier: Optional[str] = Field(None, description="Filter by carrier")
    service_level: Optional[ServiceLevel] = Field(None, description="Filter by service level")
    operator_id: Optional[str] = Field(None, description="Filter by operator ID")
    dock_door: Optional[str] = Field(None, description="Filter by dock door")
    start_date: Optional[datetime] = Field(None, description="Filter shipments after this date")
    end_date: Optional[datetime] = Field(None, description="Filter shipments before this date")
    min_weight: Optional[Decimal] = Field(None, ge=0, description="Minimum weight filter")
    max_weight: Optional[Decimal] = Field(None, ge=0, description="Maximum weight filter")
    
    @validator("end_date")
    def validate_date_range(cls, v, values):
        """Validate that end date is after start date."""
        if v and "start_date" in values and values["start_date"] and v < values["start_date"]:
            raise ValueError("End date must be after start date")
        return v


class ShipmentStatsResponse(BaseSchema):
    """Schema for shipment statistics response."""
    
    total_shipments: int = Field(..., description="Total number of shipments")
    total_weight: Decimal = Field(..., description="Total weight shipped")
    total_cost: Decimal = Field(..., description="Total shipping cost")
    average_delivery_time: Optional[Decimal] = Field(None, description="Average delivery time in hours")
    on_time_delivery_rate: Optional[Decimal] = Field(None, description="On-time delivery rate percentage")
    by_status: dict = Field(default_factory=dict, description="Statistics by shipment status")
    by_carrier: dict = Field(default_factory=dict, description="Statistics by carrier")
    by_shipment_type: dict = Field(default_factory=dict, description="Statistics by shipment type")
