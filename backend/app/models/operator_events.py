"""
Pydantic models for operator events.

This module contains all schemas related to staff activities, performance metrics,
and training compliance tracking.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import Field, validator

from app.models.base import BaseSchema, TimestampMixin, FilterParams


class Shift(str, Enum):
    """Shift enumeration."""
    DAY = "day"
    EVENING = "evening"
    NIGHT = "night"
    WEEKEND = "weekend"


class Department(str, Enum):
    """Department enumeration."""
    RECEIVING = "receiving"
    STORAGE = "storage"
    PICKING = "picking"
    PACKING = "packing"
    SHIPPING = "shipping"
    MAINTENANCE = "maintenance"


class Role(str, Enum):
    """Role enumeration."""
    PICKER = "picker"
    DRIVER = "driver"
    SUPERVISOR = "supervisor"
    TRAINER = "trainer"
    QUALITY_CONTROL = "quality_control"


class ExperienceLevel(str, Enum):
    """Experience level enumeration."""
    TRAINEE = "trainee"
    JUNIOR = "junior"
    SENIOR = "senior"
    EXPERT = "expert"


class ActivityType(str, Enum):
    """Activity type enumeration."""
    CLOCK_IN = "clock_in"
    CLOCK_OUT = "clock_out"
    BREAK_START = "break_start"
    BREAK_END = "break_end"
    TASK_ASSIGNMENT = "task_assignment"
    TRAINING = "training"
    SAFETY_CHECK = "safety_check"


class TrainingStatus(str, Enum):
    """Training status enumeration."""
    NOT_REQUIRED = "not_required"
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    EXPIRED = "expired"


class OperatorEventBase(BaseSchema):
    """Base operator event schema with common fields."""
    
    event_id: str = Field(..., description="Unique event identifier", pattern=r"^OPE-\d{8}-[A-Z0-9]{6}$")
    operator_id: str = Field(..., description="Operator identifier", pattern=r"^OP-[A-Z0-9]{6}$")
    employee_id: str = Field(..., description="Employee ID", pattern=r"^EMP-[A-Z0-9]{8}$")
    first_name: str = Field(..., min_length=1, max_length=50, description="Operator first name")
    last_name: str = Field(..., min_length=1, max_length=50, description="Operator last name")
    shift: Shift = Field(..., description="Work shift")
    department: Department = Field(..., description="Department")
    role: Role = Field(..., description="Role")
    experience_level: Optional[ExperienceLevel] = Field(None, description="Experience level")
    certifications: Optional[List[str]] = Field(default_factory=list, description="Certifications")
    activity_type: ActivityType = Field(..., description="Activity type")
    location: Optional[str] = Field(None, description="Current location", pattern=r"^[A-Z]-\d{2}-\d{2}$")
    zone: Optional[str] = Field(None, description="Current warehouse zone")
    productivity_score: Optional[int] = Field(None, ge=0, le=100, description="Productivity score (0-100)")
    tasks_completed: Optional[int] = Field(None, ge=0, description="Number of tasks completed in shift")
    hours_worked: Optional[Decimal] = Field(None, ge=0, description="Hours worked in current shift")
    overtime_hours: Optional[Decimal] = Field(None, ge=0, description="Overtime hours")
    safety_score: Optional[int] = Field(None, ge=0, le=100, description="Safety compliance score (0-100)")
    equipment_assigned: Optional[List[str]] = Field(default_factory=list, description="Equipment IDs assigned")
    supervisor_id: Optional[str] = Field(None, description="Supervisor operator ID", pattern=r"^OP-[A-Z0-9]{6}$")
    training_status: Optional[TrainingStatus] = Field(None, description="Training status")
    performance_metrics: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Performance metrics")
    timestamp: datetime = Field(..., description="Event timestamp")
    
    @validator("performance_metrics")
    def validate_performance_metrics(cls, v):
        """Validate performance metrics structure."""
        if v:
            # Ensure numeric values are properly typed
            for key, value in v.items():
                if isinstance(value, (int, float)) and value < 0:
                    raise ValueError(f"Performance metric '{key}' cannot be negative")
        return v
    
    @validator("overtime_hours")
    def validate_overtime_hours(cls, v, values):
        """Validate overtime hours against total hours worked."""
        if v and "hours_worked" in values and values["hours_worked"] and v > values["hours_worked"]:
            raise ValueError("Overtime hours cannot exceed total hours worked")
        return v


class OperatorEventCreate(OperatorEventBase):
    """Schema for creating a new operator event."""
    
    # All fields from base are required for creation
    pass


class OperatorEventUpdate(BaseSchema):
    """Schema for updating an existing operator event."""
    
    location: Optional[str] = Field(None, description="Current location", pattern=r"^[A-Z]-\d{2}-\d{2}$")
    zone: Optional[str] = Field(None, description="Current warehouse zone")
    productivity_score: Optional[int] = Field(None, ge=0, le=100, description="Productivity score (0-100)")
    tasks_completed: Optional[int] = Field(None, ge=0, description="Number of tasks completed in shift")
    hours_worked: Optional[Decimal] = Field(None, ge=0, description="Hours worked in current shift")
    overtime_hours: Optional[Decimal] = Field(None, ge=0, description="Overtime hours")
    safety_score: Optional[int] = Field(None, ge=0, le=100, description="Safety compliance score (0-100)")
    equipment_assigned: Optional[List[str]] = Field(None, description="Equipment IDs assigned")
    training_status: Optional[TrainingStatus] = Field(None, description="Training status")
    performance_metrics: Optional[Dict[str, Any]] = Field(None, description="Performance metrics")


class OperatorEventResponse(OperatorEventBase, TimestampMixin):
    """Schema for operator event response."""
    
    id: int = Field(..., description="Database record ID")


class OperatorEventListResponse(BaseSchema):
    """Schema for operator event list response with pagination."""
    
    events: List[OperatorEventResponse] = Field(..., description="List of operator events")
    total: int = Field(..., description="Total number of events")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")


class OperatorEventFilterParams(FilterParams):
    """Filter parameters for operator event queries."""
    
    operator_id: Optional[str] = Field(None, description="Filter by operator ID")
    employee_id: Optional[str] = Field(None, description="Filter by employee ID")
    shift: Optional[Shift] = Field(None, description="Filter by shift")
    department: Optional[Department] = Field(None, description="Filter by department")
    role: Optional[Role] = Field(None, description="Filter by role")
    activity_type: Optional[ActivityType] = Field(None, description="Filter by activity type")
    zone: Optional[str] = Field(None, description="Filter by warehouse zone")
    supervisor_id: Optional[str] = Field(None, description="Filter by supervisor ID")
    training_status: Optional[TrainingStatus] = Field(None, description="Filter by training status")
    start_date: Optional[datetime] = Field(None, description="Filter events after this date")
    end_date: Optional[datetime] = Field(None, description="Filter events before this date")
    min_productivity: Optional[int] = Field(None, ge=0, le=100, description="Minimum productivity score")
    min_safety_score: Optional[int] = Field(None, ge=0, le=100, description="Minimum safety score")
    
    @validator("end_date")
    def validate_date_range(cls, v, values):
        """Validate that end date is after start date."""
        if v and "start_date" in values and values["start_date"] and v < values["start_date"]:
            raise ValueError("End date must be after start date")
        return v


class OperatorStatsResponse(BaseSchema):
    """Schema for operator statistics response."""
    
    total_events: int = Field(..., description="Total number of events")
    active_operators: int = Field(..., description="Number of active operators")
    average_productivity: Optional[Decimal] = Field(None, description="Average productivity score")
    average_safety_score: Optional[Decimal] = Field(None, description="Average safety score")
    total_hours_worked: Decimal = Field(..., description="Total hours worked")
    total_overtime_hours: Decimal = Field(..., description="Total overtime hours")
    by_department: dict = Field(default_factory=dict, description="Statistics by department")
    by_shift: dict = Field(default_factory=dict, description="Statistics by shift")
    by_role: dict = Field(default_factory=dict, description="Statistics by role")
    training_compliance: dict = Field(default_factory=dict, description="Training compliance statistics")


class OperatorPerformanceResponse(BaseSchema):
    """Schema for individual operator performance response."""
    
    operator_id: str = Field(..., description="Operator identifier")
    employee_id: str = Field(..., description="Employee ID")
    full_name: str = Field(..., description="Full name")
    department: Department = Field(..., description="Department")
    role: Role = Field(..., description="Role")
    current_productivity: Optional[int] = Field(None, description="Current productivity score")
    current_safety_score: Optional[int] = Field(None, description="Current safety score")
    total_tasks_completed: int = Field(..., description="Total tasks completed")
    total_hours_worked: Decimal = Field(..., description="Total hours worked")
    average_performance: dict = Field(default_factory=dict, description="Average performance metrics")
    recent_activities: List[str] = Field(default_factory=list, description="Recent activity types")
