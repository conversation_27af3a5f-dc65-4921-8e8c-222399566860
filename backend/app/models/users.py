"""
User models and schemas for authentication.

This module contains user-related Pydantic models for authentication,
user management, and authorization in the warehouse management system.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field, ConfigDict
from app.models.base import BaseSchema, TimestampMixin


class UserBase(BaseSchema):
    """Base user schema with common fields."""
    
    email: EmailStr = Field(..., description="User email address")
    full_name: Optional[str] = Field(None, description="User's full name")
    is_active: bool = Field(True, description="Whether the user account is active")


class UserCreate(UserBase):
    """Schema for creating a new user."""
    
    password: str = Field(..., min_length=8, description="User password (minimum 8 characters)")


class UserUpdate(BaseSchema):
    """Schema for updating user information."""
    
    email: Optional[EmailStr] = Field(None, description="User email address")
    full_name: Optional[str] = Field(None, description="User's full name")
    is_active: Optional[bool] = Field(None, description="Whether the user account is active")
    password: Optional[str] = Field(None, min_length=8, description="New password")


class UserInDB(UserBase, TimestampMixin):
    """User schema as stored in database."""
    
    id: int = Field(..., description="User ID")
    hashed_password: str = Field(..., description="Hashed password")


class User(UserBase, TimestampMixin):
    """User schema for API responses (without password)."""
    
    id: int = Field(..., description="User ID")


class UserLogin(BaseSchema):
    """Schema for user login."""
    
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")


class Token(BaseSchema):
    """JWT token response schema."""
    
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class TokenData(BaseSchema):
    """Token payload data."""
    
    email: Optional[str] = Field(None, description="User email from token")
    user_id: Optional[int] = Field(None, description="User ID from token")


class PasswordReset(BaseSchema):
    """Schema for password reset request."""
    
    email: EmailStr = Field(..., description="User email address")


class PasswordResetConfirm(BaseSchema):
    """Schema for password reset confirmation."""
    
    token: str = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")
