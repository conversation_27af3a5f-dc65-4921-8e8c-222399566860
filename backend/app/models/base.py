"""
Base Pydantic models and common schemas.

This module contains base classes and common schemas used across all models
in the warehouse management system.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field


class BaseSchema(BaseModel):
    """Base schema with common configuration."""

    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True,
    )


class TimestampMixin(BaseSchema):
    """Mixin for models with timestamp fields."""

    created_at: datetime = Field(..., description="Record creation timestamp")
    updated_at: datetime = Field(..., description="Record last update timestamp")


class PaginationParams(BaseSchema):
    """Pagination parameters for list endpoints."""

    page: int = Field(1, ge=1, description="Page number (1-based)")
    size: int = Field(50, ge=1, le=1000, description="Page size")

    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.size


class PaginatedResponse(BaseSchema):
    """Generic paginated response wrapper."""

    items: List[Any] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")

    @classmethod
    def create(
        cls,
        items: List[Any],
        total: int,
        page: int,
        size: int,
    ) -> "PaginatedResponse":
        """Create a paginated response."""
        pages = (total + size - 1) // size  # Ceiling division
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages,
        )


class FilterParams(BaseSchema):
    """Base filter parameters."""

    search: Optional[str] = Field(None, description="Search term")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: Optional[str] = Field(
        "asc", pattern="^(asc|desc)$", description="Sort order"
    )


class ErrorResponse(BaseSchema):
    """Standard error response schema."""

    error: Dict[str, Any] = Field(..., description="Error details")

    class ErrorDetail(BaseSchema):
        code: str = Field(..., description="Error code")
        message: str = Field(..., description="Error message")
        details: Optional[Dict[str, Any]] = Field(
            None, description="Additional error details"
        )


class HealthResponse(BaseSchema):
    """Health check response schema."""

    status: str = Field(..., description="Service status")
    service: str = Field(..., description="Service name")
    timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="Check timestamp"
    )
    version: Optional[str] = Field(None, description="Service version")
    dependencies: Optional[Dict[str, str]] = Field(
        None, description="Dependency status"
    )


class BulkOperationResponse(BaseSchema):
    """Response for bulk operations."""

    total: int = Field(..., description="Total number of items processed")
    successful: int = Field(..., description="Number of successful operations")
    failed: int = Field(..., description="Number of failed operations")
    errors: List[Dict[str, Any]] = Field(
        default_factory=list, description="List of errors"
    )


# Common field types and validators
class LocationField(str):
    """Custom field type for warehouse locations."""

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError("string required")

        # Validate location format (e.g., A-01-02)
        parts = v.split("-")
        if len(parts) != 3:
            raise ValueError("Location must be in format A-01-02")

        if not parts[0].isalpha() or not parts[1].isdigit() or not parts[2].isdigit():
            raise ValueError("Invalid location format")

        return v


class SKUField(str):
    """Custom field type for SKU validation."""

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError("string required")

        # Validate SKU format (e.g., SKU-XXXXXXXX)
        if not v.startswith("SKU-") or len(v) != 12:
            raise ValueError("SKU must be in format SKU-XXXXXXXX")

        return v


class OperatorIDField(str):
    """Custom field type for operator ID validation."""

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not isinstance(v, str):
            raise TypeError("string required")

        # Validate operator ID format (e.g., OP-XXXXXX)
        if not v.startswith("OP-") or len(v) != 9:
            raise ValueError("Operator ID must be in format OP-XXXXXX")

        return v
