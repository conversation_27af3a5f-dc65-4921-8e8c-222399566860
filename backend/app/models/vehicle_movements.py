"""
Pydantic models for vehicle movements.

This module contains all schemas related to vehicle and equipment movements
including tracking, route efficiency, and safety metrics.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional

from pydantic import Field, validator

from app.models.base import BaseSchema, FilterParams, TimestampMixin


class VehicleType(str, Enum):
    """Vehicle type enumeration."""

    FORKLIFT = "forklift"
    REACH_TRUCK = "reach_truck"
    PALLET_JACK = "pallet_jack"
    ORDER_PICKER = "order_picker"
    TUGGER = "tugger"
    AGV = "agv"


class MovementType(str, Enum):
    """Movement type enumeration."""

    TRANSPORT = "transport"
    POSITIONING = "positioning"
    MAINTENANCE = "maintenance"
    CHARGING = "charging"
    IDLE = "idle"


class LoadStatus(str, Enum):
    """Load status enumeration."""

    EMPTY = "empty"
    LOADED = "loaded"
    PARTIAL = "partial"


class VehicleMovementBase(BaseSchema):
    """Base vehicle movement schema with common fields."""

    movement_id: str = Field(
        ..., description="Unique movement identifier", pattern=r"^MOV-\d{8}-[A-Z0-9]{6}$"
    )
    vehicle_id: str = Field(
        ..., description="Vehicle identifier", pattern=r"^VEH-[A-Z0-9]{6}$"
    )
    vehicle_type: VehicleType = Field(..., description="Type of vehicle")
    operator_id: str = Field(
        ..., description="Operator operating vehicle", pattern=r"^OP-[A-Z0-9]{6}$"
    )
    start_location: str = Field(
        ..., description="Starting location", pattern=r"^[A-Z]-\d{2}-\d{2}$"
    )
    end_location: str = Field(
        ..., description="Ending location", pattern=r"^[A-Z]-\d{2}-\d{2}$"
    )
    movement_type: MovementType = Field(..., description="Type of movement")
    load_status: Optional[LoadStatus] = Field(None, description="Load status")
    cargo_weight_kg: Optional[Decimal] = Field(
        None, ge=0, description="Weight of cargo in kilograms"
    )
    distance_meters: Optional[Decimal] = Field(
        None, ge=0, description="Distance traveled in meters"
    )
    start_time: datetime = Field(..., description="Movement start time")
    end_time: Optional[datetime] = Field(None, description="Movement completion time")
    duration_seconds: Optional[int] = Field(
        None, ge=0, description="Duration of movement"
    )
    average_speed_mps: Optional[Decimal] = Field(
        None, ge=0, description="Average speed in meters per second"
    )
    fuel_consumption_liters: Optional[Decimal] = Field(
        None, ge=0, description="Fuel consumption in liters"
    )
    battery_level_start: Optional[int] = Field(
        None, ge=0, le=100, description="Battery level at start (percentage)"
    )
    battery_level_end: Optional[int] = Field(
        None, ge=0, le=100, description="Battery level at end (percentage)"
    )
    route_efficiency: Optional[Decimal] = Field(
        None, ge=0, le=1, description="Route efficiency score (0-1)"
    )
    zone_from: Optional[str] = Field(None, description="Source warehouse zone")
    zone_to: Optional[str] = Field(None, description="Destination warehouse zone")
    associated_task_id: Optional[str] = Field(
        None, description="Associated task or pick ID"
    )
    safety_incidents: Optional[List[str]] = Field(
        default_factory=list, description="Safety incidents"
    )

    @validator("end_time")
    def validate_end_time(cls, v, values):
        """Validate that end time is after start time."""
        if v and "start_time" in values and v < values["start_time"]:
            raise ValueError("End time must be after start time")
        return v

    @validator("duration_seconds")
    def calculate_duration(cls, v, values):
        """Calculate duration if not provided and both start/end times are available."""
        if (
            v is None
            and "start_time" in values
            and "end_time" in values
            and values["end_time"]
        ):
            delta = values["end_time"] - values["start_time"]
            return int(delta.total_seconds())
        return v

    @validator("average_speed_mps")
    def calculate_average_speed(cls, v, values):
        """Calculate average speed if not provided."""
        if (
            v is None
            and "distance_meters" in values
            and values["distance_meters"]
            and "duration_seconds" in values
            and values["duration_seconds"]
            and values["duration_seconds"] > 0
        ):
            return values["distance_meters"] / values["duration_seconds"]
        return v

    @validator("battery_level_end")
    def validate_battery_levels(cls, v, values):
        """Validate battery level consistency."""
        if (
            v is not None
            and "battery_level_start" in values
            and values["battery_level_start"] is not None
            and v > values["battery_level_start"]
        ):
            # Battery level can only increase during charging
            if (
                "movement_type" in values
                and values["movement_type"] != MovementType.CHARGING
            ):
                raise ValueError(
                    "Battery level can only increase during charging operations"
                )
        return v


class VehicleMovementCreate(VehicleMovementBase):
    """Schema for creating a new vehicle movement."""

    # Override fields that should not be provided during creation
    end_time: Optional[datetime] = Field(None, description="Movement completion time")
    duration_seconds: Optional[int] = Field(None, description="Duration of movement")
    average_speed_mps: Optional[Decimal] = Field(
        None, description="Average speed in meters per second"
    )


class VehicleMovementUpdate(BaseSchema):
    """Schema for updating an existing vehicle movement."""

    end_location: Optional[str] = Field(
        None, description="Ending location", pattern=r"^[A-Z]-\d{2}-\d{2}$"
    )
    load_status: Optional[LoadStatus] = Field(None, description="Load status")
    cargo_weight_kg: Optional[Decimal] = Field(
        None, ge=0, description="Weight of cargo in kilograms"
    )
    distance_meters: Optional[Decimal] = Field(
        None, ge=0, description="Distance traveled in meters"
    )
    end_time: Optional[datetime] = Field(None, description="Movement completion time")
    fuel_consumption_liters: Optional[Decimal] = Field(
        None, ge=0, description="Fuel consumption in liters"
    )
    battery_level_end: Optional[int] = Field(
        None, ge=0, le=100, description="Battery level at end (percentage)"
    )
    route_efficiency: Optional[Decimal] = Field(
        None, ge=0, le=1, description="Route efficiency score (0-1)"
    )
    zone_to: Optional[str] = Field(None, description="Destination warehouse zone")
    safety_incidents: Optional[List[str]] = Field(None, description="Safety incidents")


class VehicleMovementResponse(VehicleMovementBase, TimestampMixin):
    """Schema for vehicle movement response."""

    id: int = Field(..., description="Database record ID")


class VehicleMovementListResponse(BaseSchema):
    """Schema for vehicle movement list response with pagination."""

    movements: List[VehicleMovementResponse] = Field(
        ..., description="List of vehicle movements"
    )
    total: int = Field(..., description="Total number of movements")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")


class VehicleMovementFilterParams(FilterParams):
    """Filter parameters for vehicle movement queries."""

    vehicle_id: Optional[str] = Field(None, description="Filter by vehicle ID")
    vehicle_type: Optional[VehicleType] = Field(
        None, description="Filter by vehicle type"
    )
    operator_id: Optional[str] = Field(None, description="Filter by operator ID")
    movement_type: Optional[MovementType] = Field(
        None, description="Filter by movement type"
    )
    load_status: Optional[LoadStatus] = Field(None, description="Filter by load status")
    zone_from: Optional[str] = Field(None, description="Filter by source zone")
    zone_to: Optional[str] = Field(None, description="Filter by destination zone")
    start_date: Optional[datetime] = Field(
        None, description="Filter movements after this date"
    )
    end_date: Optional[datetime] = Field(
        None, description="Filter movements before this date"
    )
    min_distance: Optional[Decimal] = Field(
        None, ge=0, description="Minimum distance filter"
    )
    max_distance: Optional[Decimal] = Field(
        None, ge=0, description="Maximum distance filter"
    )

    @validator("end_date")
    def validate_date_range(cls, v, values):
        """Validate that end date is after start date."""
        if (
            v
            and "start_date" in values
            and values["start_date"]
            and v < values["start_date"]
        ):
            raise ValueError("End date must be after start date")
        return v

    @validator("max_distance")
    def validate_distance_range(cls, v, values):
        """Validate that max distance is greater than min distance."""
        if (
            v
            and "min_distance" in values
            and values["min_distance"]
            and v < values["min_distance"]
        ):
            raise ValueError("Maximum distance must be greater than minimum distance")
        return v


class VehicleMovementStatsResponse(BaseSchema):
    """Schema for vehicle movement statistics response."""

    total_movements: int = Field(..., description="Total number of movements")
    total_distance: Decimal = Field(..., description="Total distance traveled")
    total_duration: int = Field(..., description="Total movement duration in seconds")
    average_speed: Optional[Decimal] = Field(
        None, description="Average speed across all movements"
    )
    fuel_efficiency: Optional[Decimal] = Field(
        None, description="Average fuel efficiency"
    )
    route_efficiency_avg: Optional[Decimal] = Field(
        None, description="Average route efficiency"
    )
    safety_incidents_count: int = Field(
        ..., description="Total number of safety incidents"
    )
    by_vehicle_type: dict = Field(
        default_factory=dict, description="Statistics grouped by vehicle type"
    )
    by_movement_type: dict = Field(
        default_factory=dict, description="Statistics grouped by movement type"
    )
