"""
Pydantic models for pick operations.

This module contains all schemas related to warehouse pick operations including
pick creation, updates, responses, and filtering.
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional

from pydantic import Field, validator

from app.models.base import BaseSchema, FilterParams, TimestampMixin


class PickStatus(str, Enum):
    """Pick operation status enumeration."""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SHORT_PICKED = "short_picked"
    CANCELLED = "cancelled"


class PickMethod(str, Enum):
    """Pick method enumeration."""

    MANUAL = "manual"
    VOICE_DIRECTED = "voice_directed"
    RF_SCANNER = "rf_scanner"
    PICK_TO_LIGHT = "pick_to_light"


class Priority(str, Enum):
    """Priority level enumeration."""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class PickBase(BaseSchema):
    """Base pick schema with common fields."""

    pick_id: str = Field(
        ..., description="Unique pick identifier", pattern=r"^PICK-\d{8}-[A-Z0-9]{6}$"
    )
    order_id: str = Field(
        ...,
        description="Associated order identifier",
        pattern=r"^ORD-\d{8}-[A-Z0-9]{4}$",
    )
    operator_id: str = Field(
        ..., description="Operator performing pick", pattern=r"^OP-[A-Z0-9]{6}$"
    )
    item_sku: str = Field(
        ..., description="Stock keeping unit", pattern=r"^SKU-[A-Z0-9]{8}$"
    )
    item_description: Optional[str] = Field(
        None, description="Human-readable item description"
    )
    quantity_requested: int = Field(..., ge=1, description="Quantity requested to pick")
    quantity_picked: int = Field(..., ge=0, description="Actual quantity picked")
    location_from: str = Field(
        ..., description="Source location", pattern=r"^[A-Z]-\d{2}-\d{2}$"
    )
    location_to: Optional[str] = Field(
        None, description="Destination location", pattern=r"^[A-Z]-\d{2}-\d{2}$"
    )
    pick_status: PickStatus = Field(..., description="Pick operation status")
    pick_method: Optional[PickMethod] = Field(None, description="Pick method used")
    start_time: datetime = Field(..., description="Pick operation start time")
    end_time: Optional[datetime] = Field(
        None, description="Pick operation completion time"
    )
    duration_seconds: Optional[int] = Field(
        None, ge=0, description="Duration of pick operation"
    )
    weight_kg: Optional[Decimal] = Field(
        None, ge=0, description="Weight of picked items in kilograms"
    )
    priority: Optional[Priority] = Field(None, description="Pick priority level")
    batch_id: Optional[str] = Field(
        None, description="Batch identifier for grouped picks"
    )
    zone: Optional[str] = Field(None, description="Warehouse zone")
    equipment_used: Optional[List[str]] = Field(
        default_factory=list, description="Equipment used for pick"
    )

    @validator("quantity_picked")
    def validate_quantity_picked(cls, v, values):
        """Validate that picked quantity doesn't exceed requested quantity."""
        if "quantity_requested" in values and v > values["quantity_requested"]:
            raise ValueError("Picked quantity cannot exceed requested quantity")
        return v

    @validator("end_time")
    def validate_end_time(cls, v, values):
        """Validate that end time is after start time."""
        if v and "start_time" in values and v < values["start_time"]:
            raise ValueError("End time must be after start time")
        return v

    @validator("duration_seconds")
    def calculate_duration(cls, v, values):
        """Calculate duration if not provided and both start/end times are available."""
        if (
            v is None
            and "start_time" in values
            and "end_time" in values
            and values["end_time"]
        ):
            delta = values["end_time"] - values["start_time"]
            return int(delta.total_seconds())
        return v


class PickCreate(PickBase):
    """Schema for creating a new pick."""

    # Override fields that should not be provided during creation
    pick_status: PickStatus = Field(
        PickStatus.PENDING, description="Pick operation status"
    )
    end_time: Optional[datetime] = Field(
        None, description="Pick operation completion time"
    )
    duration_seconds: Optional[int] = Field(
        None, description="Duration of pick operation"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "pick_id": "PICK-20240314-ABC123",
                "order_id": "ORD-20240314-001",
                "item_sku": "SKU-WIDGET-001",
                "quantity_requested": 10,
                "quantity_picked": 0,
                "location_from": "A-01-05",
                "location_to": "PACK-01",
                "operator_id": "OP001",
                "pick_status": "pending",
                "pick_method": "rf_scanner",
                "priority": "normal",
                "start_time": "2024-03-14T10:30:00Z",
                "notes": "Handle with care - fragile items",
            }
        }
    }


class PickUpdate(BaseSchema):
    """Schema for updating an existing pick."""

    quantity_picked: Optional[int] = Field(
        None, ge=0, description="Actual quantity picked"
    )
    location_to: Optional[str] = Field(
        None, description="Destination location", pattern=r"^[A-Z]-\d{2}-\d{2}$"
    )
    pick_status: Optional[PickStatus] = Field(None, description="Pick operation status")
    pick_method: Optional[PickMethod] = Field(None, description="Pick method used")
    end_time: Optional[datetime] = Field(
        None, description="Pick operation completion time"
    )
    weight_kg: Optional[Decimal] = Field(
        None, ge=0, description="Weight of picked items in kilograms"
    )
    equipment_used: Optional[List[str]] = Field(
        None, description="Equipment used for pick"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "quantity_picked": 8,
                "pick_status": "completed",
                "end_time": "2024-03-14T10:45:00Z",
                "weight_kg": 2.5,
                "equipment_used": ["RF_SCANNER", "PALLET_JACK"],
                "notes": "Short picked due to damaged inventory",
            }
        }
    }


class PickResponse(PickBase, TimestampMixin):
    """Schema for pick response."""

    id: int = Field(..., description="Database record ID")


class PickListResponse(BaseSchema):
    """Schema for pick list response with pagination."""

    picks: List[PickResponse] = Field(..., description="List of picks")
    total: int = Field(..., description="Total number of picks")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")


class PickFilterParams(FilterParams):
    """Filter parameters for pick queries."""

    operator_id: Optional[str] = Field(None, description="Filter by operator ID")
    item_sku: Optional[str] = Field(None, description="Filter by item SKU")
    pick_status: Optional[PickStatus] = Field(None, description="Filter by pick status")
    priority: Optional[Priority] = Field(None, description="Filter by priority")
    zone: Optional[str] = Field(None, description="Filter by warehouse zone")
    batch_id: Optional[str] = Field(None, description="Filter by batch ID")
    start_date: Optional[datetime] = Field(
        None, description="Filter picks after this date"
    )
    end_date: Optional[datetime] = Field(
        None, description="Filter picks before this date"
    )

    @validator("end_date")
    def validate_date_range(cls, v, values):
        """Validate that end date is after start date."""
        if (
            v
            and "start_date" in values
            and values["start_date"]
            and v < values["start_date"]
        ):
            raise ValueError("End date must be after start date")
        return v


class PickStatsResponse(BaseSchema):
    """Schema for pick statistics response."""

    total_picks: int = Field(..., description="Total number of picks")
    completed_picks: int = Field(..., description="Number of completed picks")
    pending_picks: int = Field(..., description="Number of pending picks")
    in_progress_picks: int = Field(..., description="Number of picks in progress")
    short_picked: int = Field(..., description="Number of short picks")
    cancelled_picks: int = Field(..., description="Number of cancelled picks")
    average_duration: Optional[float] = Field(
        None, description="Average pick duration in seconds"
    )
    total_items_picked: int = Field(..., description="Total items picked")
    pick_accuracy: Optional[float] = Field(None, description="Pick accuracy percentage")


class BulkPickUpdate(BaseSchema):
    """Schema for bulk pick updates."""

    pick_ids: List[str] = Field(..., description="List of pick IDs to update")
    updates: PickUpdate = Field(..., description="Updates to apply to all picks")
