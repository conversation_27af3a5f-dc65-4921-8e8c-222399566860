# API Testing Checklist

This document tracks the progress of API endpoint testing. Each item will be marked as completed once testing is finished.

## Phase 1: Authentication & User Management

- [x] POST /api/v1/auth/signup - Successful registration
- [x] POST /api/v1/auth/signup - Registration with existing email (should fail)
- [x] POST /api/v1/auth/signup - Registration with invalid data
- [x] POST /api/v1/auth/login - Successful login with valid credentials
- [x] POST /api/v1/auth/login - Login with incorrect email
- [x] POST /api/v1/auth/login - Login with incorrect password
- [x] POST /api/v1/auth/login - Login with inactive user
- [x] GET /api/v1/auth/me - Retrieve current user with valid token
- [x] GET /api/v1/auth/me - Access without token (should fail)
- [x] GET /api/v1/auth/me - Access with invalid token (should fail)
- [x] POST /api/v1/auth/logout - Successful logout
- [x] POST /api/v1/auth/password-reset - Request with existing email
- [x] POST /api/v1/auth/password-reset - Request with non-existing email
- [x] POST /api/v1/auth/password-reset - Request with invalid email format
- [x] POST /api/v1/auth/password-reset/confirm - Password reset confirmation
- [x] POST /api/v1/auth/refresh - Token refresh with valid token

## Phase 2: Core Warehouse Operations

- [x] POST /api/v1/picks/ - Create new pick operation
- [x] POST /api/v1/picks/ - Create pick with invalid data
- [x] POST /api/v1/picks/ - Create pick with existing pick_id
- [x] GET /api/v1/picks/{pick_id} - Retrieve pick by ID
- [x] GET /api/v1/picks/{pick_id} - Retrieve non-existing pick (should fail)
- [x] PUT /api/v1/picks/{pick_id} - Update pick
- [x] PUT /api/v1/picks/{pick_id} - Update non-existing pick (should fail)
- [x] DELETE /api/v1/picks/{pick_id} - Delete pick
- [x] DELETE /api/v1/picks/{pick_id} - Delete non-existing pick (should fail)
- [x] GET /api/v1/picks/ - List picks with pagination
- [x] GET /api/v1/picks/ - Filter picks by parameters
- [x] GET /api/v1/picks/ - Sort picks
- [x] GET /api/v1/picks/statistics - Retrieve pick statistics
- [x] GET /api/v1/picks/operator/{operator_id} - Retrieve picks for operator
- [x] GET /api/v1/picks/batch/{batch_id} - Retrieve picks for batch
- [x] POST /api/v1/vehicle-movements/ - Create new vehicle movement
- [x] GET /api/v1/vehicle-movements/{movement_id} - Retrieve vehicle movement by ID
- [x] GET /api/v1/vehicle-movements/statistics - Retrieve vehicle movement statistics
- [x] POST /api/v1/operator-events/ - Create new operator event
- [x] GET /api/v1/operator-events/{event_id} - Retrieve operator event by ID
- [x] GET /api/v1/operator-events/statistics - Retrieve operator event statistics

## Phase 3: Inventory, Shipments & Error Handling

- [x] POST /api/v1/inventory-transactions/ - Create new inventory transaction
- [x] GET /api/v1/inventory-transactions/{transaction_id} - Retrieve transaction by ID
- [x] GET /api/v1/inventory-transactions/statistics - Retrieve transaction statistics
- [x] POST /api/v1/shipments/ - Create new shipment
- [x] GET /api/v1/shipments/{shipment_id} - Retrieve shipment by ID
- [x] GET /api/v1/shipments/statistics - Retrieve shipment statistics
- [x] POST /api/v1/failed-messages/ - Create new failed message
- [x] GET /api/v1/failed-messages/{message_id} - Retrieve failed message by ID
- [x] PUT /api/v1/failed-messages/{message_id}/retry - Retry failed message
- [x] GET /api/v1/failed-messages/statistics - Retrieve failed message statistics