# API Testing Plan

This document outlines the testing plan for the FastAPI warehouse management system endpoints. The testing is divided into three phases based on endpoint functionality and dependencies.

## Phase 1: Authentication & User Management

This phase focuses on testing authentication-related endpoints which are prerequisites for accessing protected resources in later phases.

### Endpoints to Test:

1. **POST /api/v1/auth/signup**
   - Test successful user registration
   - Test registration with existing email (should fail)
   - Test registration with invalid data (missing fields, invalid email format)

2. **POST /api/v1/auth/login**
   - Test successful login with valid credentials
   - Test login with incorrect email
   - Test login with incorrect password
   - Test login with inactive user

3. **GET /api/v1/auth/me**
   - Test retrieving current user information with valid token
   - Test accessing without token (should fail)
   - Test accessing with invalid token (should fail)

4. **POST /api/v1/auth/logout**
   - Test successful logout with valid token

5. **POST /api/v1/auth/password-reset**
   - Test password reset request with existing email
   - Test password reset request with non-existing email
   - Test password reset request with invalid email format

6. **POST /api/v1/auth/password-reset/confirm**
   - Test password reset confirmation

7. **POST /api/v1/auth/refresh**
   - Test token refresh with valid token

## Phase 2: Core Warehouse Operations

This phase tests the core warehouse functionality endpoints that are essential for daily operations.

### Endpoints to Test:

1. **POST /api/v1/picks/**
   - Test creating a new pick operation
   - Test creating pick with invalid data
   - Test creating pick with existing pick_id

2. **GET /api/v1/picks/{pick_id}**
   - Test retrieving a pick by ID
   - Test retrieving non-existing pick (should fail)

3. **PUT /api/v1/picks/{pick_id}**
   - Test updating a pick
   - Test updating non-existing pick (should fail)

4. **DELETE /api/v1/picks/{pick_id}**
   - Test deleting a pick
   - Test deleting non-existing pick (should fail)

5. **GET /api/v1/picks/**
   - Test listing picks with pagination
   - Test filtering picks by various parameters (operator_id, item_sku, etc.)
   - Test sorting picks

6. **GET /api/v1/picks/statistics**
   - Test retrieving pick statistics

7. **GET /api/v1/picks/operator/{operator_id}**
   - Test retrieving picks for a specific operator

8. **GET /api/v1/picks/batch/{batch_id}**
   - Test retrieving picks for a specific batch

9. **POST /api/v1/vehicle-movements/**
   - Test creating a new vehicle movement

10. **GET /api/v1/vehicle-movements/{movement_id}**
    - Test retrieving a vehicle movement by ID

11. **GET /api/v1/vehicle-movements/statistics**
    - Test retrieving vehicle movement statistics

12. **POST /api/v1/operator-events/**
    - Test creating a new operator event

13. **GET /api/v1/operator-events/{event_id}**
    - Test retrieving an operator event by ID

14. **GET /api/v1/operator-events/statistics**
    - Test retrieving operator event statistics

## Phase 3: Inventory, Shipments & Error Handling

This phase tests inventory management, shipment processing, and error handling functionality.

### Endpoints to Test:

1. **POST /api/v1/inventory-transactions/**
   - Test creating a new inventory transaction

2. **GET /api/v1/inventory-transactions/{transaction_id}**
   - Test retrieving an inventory transaction by ID

3. **GET /api/v1/inventory-transactions/statistics**
   - Test retrieving inventory transaction statistics

4. **POST /api/v1/shipments/**
   - Test creating a new shipment

5. **GET /api/v1/shipments/{shipment_id}**
   - Test retrieving a shipment by ID

6. **GET /api/v1/shipments/statistics**
   - Test retrieving shipment statistics

7. **POST /api/v1/failed-messages/**
   - Test creating a new failed message

8. **GET /api/v1/failed-messages/{message_id}**
   - Test retrieving a failed message by ID

9. **PUT /api/v1/failed-messages/{message_id}/retry**
   - Test retrying a failed message

10. **GET /api/v1/failed-messages/statistics**
    - Test retrieving failed message statistics

## Test Data Requirements

For comprehensive testing, we'll need to prepare test data for each endpoint that requires specific parameters or has dependencies on other entities.

## Authentication Requirements

Most endpoints (except authentication endpoints) require a valid JWT token in the Authorization header. Tests should:
1. First obtain a token via the login endpoint
2. Use this token for all subsequent authenticated requests
3. Test behavior with expired or invalid tokens

## Expected Test Outcomes

Each endpoint test should verify:
- Correct HTTP status codes for success and error cases
- Proper response data structure and content
- Error handling for invalid inputs
- Data persistence and retrieval consistency
- Proper authentication and authorization checks