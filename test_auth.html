<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Test</title>
</head>
<body>
    <h1>Authentication Test</h1>
    <div id="result"></div>
    
    <script>
        async function testAuth() {
            const resultDiv = document.getElementById('result');
            
            try {
                // Test login
                const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });
                
                if (!loginResponse.ok) {
                    throw new Error(`Login failed: ${loginResponse.status}`);
                }
                
                const loginData = await loginResponse.json();
                resultDiv.innerHTML += `<p>✅ Login successful! Token: ${loginData.access_token.substring(0, 20)}...</p>`;
                
                // Test getting current user
                const userResponse = await fetch('http://localhost:8000/api/v1/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${loginData.access_token}`
                    }
                });
                
                if (!userResponse.ok) {
                    throw new Error(`Get user failed: ${userResponse.status}`);
                }
                
                const userData = await userResponse.json();
                resultDiv.innerHTML += `<p>✅ User data retrieved: ${userData.email} (${userData.full_name})</p>`;
                
                // Test signup with new user
                const signupResponse = await fetch('http://localhost:8000/api/v1/auth/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: `test${Date.now()}@example.com`,
                        password: 'testpass123',
                        full_name: 'Test User'
                    })
                });
                
                if (!signupResponse.ok) {
                    throw new Error(`Signup failed: ${signupResponse.status}`);
                }
                
                const signupData = await signupResponse.json();
                resultDiv.innerHTML += `<p>✅ Signup successful! New user: ${signupData.email}</p>`;
                
                resultDiv.innerHTML += `<p><strong>🎉 All authentication tests passed!</strong></p>`;
                
            } catch (error) {
                resultDiv.innerHTML += `<p>❌ Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testAuth();
    </script>
</body>
</html>
