/**
 * Simple Bar Chart component using CSS
 */

import React, { memo, useMemo } from 'react'

interface ChartData {
  label: string
  value: number
  color?: string
}

interface SimpleBarChartProps {
  data: ChartData[]
  title?: string
  height?: number
  showValues?: boolean
}

const SimpleBarChart = memo(function SimpleBarChart({
  data,
  title,
  height = 200,
  showValues = true
}: SimpleBarChartProps) {
  // Memoize expensive calculations
  const chartData = useMemo(() => {
    const maxValue = Math.max(...data.map(d => d.value));

    const defaultColors = [
      '#3B82F6', // blue-500
      '#10B981', // emerald-500
      '#F59E0B', // amber-500
      '#EF4444', // red-500
      '#8B5CF6', // violet-500
      '#06B6D4', // cyan-500
    ];

    return {
      maxValue,
      defaultColors,
      bars: data.map((item, index) => ({
        ...item,
        heightPercentage: (item.value / maxValue) * 100,
        color: defaultColors[index % defaultColors.length]
      }))
    };
  }, [data]);

  const { maxValue, bars } = chartData;

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}

      <div
        className="flex items-end space-x-1 sm:space-x-2"
        style={{ height: `${height}px` }}
        role="img"
        aria-label={title || "Bar chart"}
      >
        {bars.map((item, index) => {
          const barHeight = maxValue > 0 ? (item.value / maxValue) * (height - 40) : 0

          return (
            <div key={item.label} className="flex-1 flex flex-col items-center min-w-0">
              <div className="flex flex-col items-center justify-end h-full w-full">
                {showValues && (
                  <span className="text-xs font-medium text-gray-600 mb-1 truncate">
                    {item.value}
                  </span>
                )}
                <div
                  className="w-full rounded-t transition-all duration-300 hover:opacity-80"
                  style={{
                    height: `${barHeight}px`,
                    backgroundColor: item.color,
                    minHeight: item.value > 0 ? '4px' : '0px',
                  }}
                />
              </div>
              <div className="mt-2 text-xs text-gray-500 text-center truncate w-full">
                <span className="hidden sm:inline">{item.label}</span>
                <span className="sm:hidden">{item.label.substring(0, 3)}</span>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
});

export default SimpleBarChart;
