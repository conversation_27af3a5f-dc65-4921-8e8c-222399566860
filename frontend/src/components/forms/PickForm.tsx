/**
 * Form component for creating and editing picks
 */

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '../ui/Card'
import Input from '../ui/Input'
import Select from '../ui/Select'
import Textarea from '../ui/Textarea'
import Button from '../ui/Button'
import { PickCreate, PickStatus, PickMethod, Priority } from '../../lib/types'
import { useCreatePick, useUpdatePick } from '../../hooks/use-picks'

const pickSchema = z.object({
  pick_id: z.string().min(1, 'Pick ID is required'),
  order_id: z.string().min(1, 'Order ID is required'),
  operator_id: z.string().min(1, 'Operator ID is required'),
  item_sku: z.string().min(1, 'Item SKU is required'),
  item_description: z.string().optional(),
  quantity_requested: z.number().min(1, 'Quantity must be at least 1'),
  location_from: z.string().min(1, 'Source location is required'),
  location_to: z.string().optional(),
  pick_status: z.nativeEnum(PickStatus),
  pick_method: z.nativeEnum(PickMethod).optional(),
  priority: z.nativeEnum(Priority).optional(),
  notes: z.string().optional(),
})

type PickFormData = z.infer<typeof pickSchema>

interface PickFormProps {
  initialData?: Partial<PickFormData>
  onSuccess?: () => void
  onCancel?: () => void
  mode?: 'create' | 'edit'
}

export default function PickForm({ 
  initialData, 
  onSuccess, 
  onCancel, 
  mode = 'create' 
}: PickFormProps) {
  const createPick = useCreatePick()
  const updatePick = useUpdatePick()

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<PickFormData>({
    resolver: zodResolver(pickSchema),
    defaultValues: {
      pick_status: PickStatus.PENDING,
      priority: Priority.NORMAL,
      quantity_requested: 1,
      ...initialData,
    },
  })

  const onSubmit = async (data: PickFormData) => {
    try {
      const pickData: PickCreate = {
        ...data,
        start_time: new Date().toISOString(),
        quantity_picked: 0,
      }

      if (mode === 'create') {
        await createPick.mutateAsync(pickData)
      } else {
        // For edit mode, you'd need the pick ID
        // await updatePick.mutateAsync({ pickId: initialData?.pick_id!, data: pickData })
      }

      reset()
      onSuccess?.()
    } catch (error) {
      console.error('Failed to save pick:', error)
    }
  }

  const statusOptions = Object.values(PickStatus).map(status => ({
    value: status,
    label: status.replace('_', ' ').toUpperCase(),
  }))

  const methodOptions = Object.values(PickMethod).map(method => ({
    value: method,
    label: method.replace('_', ' ').toUpperCase(),
  }))

  const priorityOptions = Object.values(Priority).map(priority => ({
    value: priority,
    label: priority.toUpperCase(),
  }))

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>
          {mode === 'create' ? 'Create New Pick' : 'Edit Pick'}
        </CardTitle>
      </CardHeader>
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Pick ID"
              {...register('pick_id')}
              error={errors.pick_id?.message}
              placeholder="PICK-20240314-ABC123"
              required
            />
            
            <Input
              label="Order ID"
              {...register('order_id')}
              error={errors.order_id?.message}
              placeholder="ORD-20240314-001"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Operator ID"
              {...register('operator_id')}
              error={errors.operator_id?.message}
              placeholder="OP-001"
              required
            />
            
            <Input
              label="Item SKU"
              {...register('item_sku')}
              error={errors.item_sku?.message}
              placeholder="SKU-WIDGET-001"
              required
            />
          </div>

          <Input
            label="Item Description"
            {...register('item_description')}
            error={errors.item_description?.message}
            placeholder="Widget Assembly Kit"
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Quantity Requested"
              type="number"
              {...register('quantity_requested', { valueAsNumber: true })}
              error={errors.quantity_requested?.message}
              min="1"
              required
            />
            
            <Select
              label="Status"
              {...register('pick_status')}
              error={errors.pick_status?.message}
              options={statusOptions}
              required
            />
            
            <Select
              label="Priority"
              {...register('priority')}
              error={errors.priority?.message}
              options={priorityOptions}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Source Location"
              {...register('location_from')}
              error={errors.location_from?.message}
              placeholder="A-01-05"
              required
            />
            
            <Input
              label="Destination Location"
              {...register('location_to')}
              error={errors.location_to?.message}
              placeholder="PACK-01"
            />
          </div>

          <Select
            label="Pick Method"
            {...register('pick_method')}
            error={errors.pick_method?.message}
            options={methodOptions}
            placeholder="Select pick method"
          />

          <Textarea
            label="Notes"
            {...register('notes')}
            error={errors.notes?.message}
            placeholder="Additional notes or special instructions..."
            rows={3}
          />
        </CardContent>

        <CardFooter className="flex justify-end space-x-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button 
            type="submit" 
            loading={isSubmitting || createPick.isPending || updatePick.isPending}
          >
            {mode === 'create' ? 'Create Pick' : 'Update Pick'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
