/**
 * Main application layout component
 * Provides the overall structure with header, sidebar, and main content area
 */

import { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import Header from './Header'
import Sidebar from './Sidebar'
import { cn } from '../../lib/utils'
import Dashboard from '../../pages/Dashboard'
import PicksList from '../../pages/picks/PicksList'
import PicksDashboard from '../../pages/picks/PicksDashboard'
import CreatePick from '../../pages/picks/CreatePick'
import OperatorsList from '../../pages/operators/OperatorsList'
import OperatorsDashboard from '../../pages/operators/OperatorsDashboard'
import VehiclesList from '../../pages/vehicles/VehiclesList'
import InventoryDashboard from '../../pages/inventory/InventoryDashboard'
import ShipmentsDashboard from '../../pages/shipments/ShipmentsDashboard'
import FailedMessages from '../../pages/system/FailedMessages'

export default function AppLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(true)

  return (
    <div className="min-h-screen bg-gray-50 pt-16">
      {/* Skip to content for accessibility */}
      <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 focus:z-[100] bg-white text-blue-700 border border-blue-200 rounded px-3 py-2">Skip to main content</a>

      {/* Header */}
      <Header
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* Mobile backdrop when sidebar is open */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/20 z-30 md:hidden"
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        />
      )}

      <div className="flex">
        {/* Sidebar */}
        <Sidebar 
          open={sidebarOpen} 
          setOpen={setSidebarOpen} 
        />
        
        {/* Main content */}
        <main
          id="main-content"
          className={cn(
            'flex-1 transition-all duration-300',
            sidebarOpen ? 'md:ml-64' : 'md:ml-16'
          )}
        >
          <div className="p-4 md:p-6 max-w-7xl mx-auto">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              
              {/* Picks routes */}
              <Route path="/picks" element={<PicksDashboard />} />
              <Route path="/picks/dashboard" element={<PicksDashboard />} />
              <Route path="/picks/create" element={<CreatePick />} />
              <Route path="/picks/list" element={<PicksList />} />

              {/* Operators routes */}
              <Route path="/operators" element={<OperatorsDashboard />} />
              <Route path="/operators/dashboard" element={<OperatorsDashboard />} />
              <Route path="/operators/list" element={<OperatorsList />} />
              <Route path="/operators/activity" element={<div>Activity Logs</div>} />

              {/* Vehicles routes */}
              <Route path="/vehicles" element={<VehiclesList />} />
              <Route path="/vehicles/tracking" element={<VehiclesList />} />
              <Route path="/vehicles/list" element={<VehiclesList />} />
              <Route path="/vehicles/metrics" element={<div>Efficiency Metrics</div>} />

              {/* Inventory routes */}
              <Route path="/inventory" element={<InventoryDashboard />} />
              <Route path="/inventory/dashboard" element={<InventoryDashboard />} />
              <Route path="/inventory/create" element={<div>New Transaction</div>} />
              <Route path="/inventory/history" element={<InventoryDashboard />} />

              {/* Shipments routes */}
              <Route path="/shipments" element={<ShipmentsDashboard />} />
              <Route path="/shipments/dashboard" element={<ShipmentsDashboard />} />
              <Route path="/shipments/create" element={<div>Create Shipment</div>} />
              <Route path="/shipments/list" element={<ShipmentsDashboard />} />

              {/* System routes */}
              <Route path="/system" element={<FailedMessages />} />
              <Route path="/system/failed-messages" element={<FailedMessages />} />
              <Route path="/system/settings" element={<div>Settings</div>} />
              
              {/* Catch all route */}
              <Route path="*" element={<div>Page Not Found</div>} />
            </Routes>
          </div>
        </main>
      </div>
    </div>
  )
}
