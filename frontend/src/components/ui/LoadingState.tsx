/**
 * Loading state component with various loading indicators
 */

import React from 'react'
import { Loader2, RefreshCw } from 'lucide-react'
import { cn } from '../../lib/utils'

interface LoadingStateProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'dots' | 'bars'
  text?: string
  className?: string
  fullScreen?: boolean
}

export function LoadingState({
  size = 'md',
  variant = 'spinner',
  text,
  className,
  fullScreen = false
}: LoadingStateProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  const renderSpinner = () => (
    <Loader2 className={cn('animate-spin text-blue-600', sizeClasses[size])} />
  )

  const renderDots = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'bg-blue-600 rounded-full animate-pulse',
            size === 'sm' ? 'h-1 w-1' : size === 'md' ? 'h-2 w-2' : 'h-3 w-3'
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  )

  const renderBars = () => (
    <div className="flex space-x-1 items-end">
      {[0, 1, 2, 3].map((i) => (
        <div
          key={i}
          className={cn(
            'bg-blue-600 animate-pulse',
            size === 'sm' ? 'w-1' : size === 'md' ? 'w-1.5' : 'w-2'
          )}
          style={{
            height: `${Math.random() * 20 + 10}px`,
            animationDelay: `${i * 0.15}s`,
            animationDuration: '1.2s'
          }}
        />
      ))}
    </div>
  )

  const renderLoader = () => {
    switch (variant) {
      case 'dots':
        return renderDots()
      case 'bars':
        return renderBars()
      default:
        return renderSpinner()
    }
  }

  const content = (
    <div className={cn(
      'flex flex-col items-center justify-center space-y-2',
      fullScreen && 'min-h-screen',
      className
    )}>
      {renderLoader()}
      {text && (
        <p className={cn('text-gray-600', textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  )

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-75 z-50 flex items-center justify-center">
        {content}
      </div>
    )
  }

  return content
}

// Specialized loading components
export function ButtonLoading({ size = 'sm' }: { size?: 'sm' | 'md' | 'lg' }) {
  return <Loader2 className={cn('animate-spin', {
    'h-3 w-3': size === 'sm',
    'h-4 w-4': size === 'md',
    'h-5 w-5': size === 'lg'
  })} />
}

export function PageLoading({ text = 'Loading...' }: { text?: string }) {
  return (
    <LoadingState
      size="lg"
      text={text}
      className="py-12"
    />
  )
}

export function InlineLoading({ text }: { text?: string }) {
  return (
    <div className="flex items-center space-x-2 text-gray-600">
      <Loader2 className="h-4 w-4 animate-spin" />
      {text && <span className="text-sm">{text}</span>}
    </div>
  )
}

export function RefreshLoading({ 
  isRefreshing, 
  onRefresh, 
  className 
}: { 
  isRefreshing: boolean; 
  onRefresh: () => void; 
  className?: string 
}) {
  return (
    <button
      onClick={onRefresh}
      disabled={isRefreshing}
      className={cn(
        'p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50',
        className
      )}
      aria-label={isRefreshing ? 'Refreshing...' : 'Refresh'}
    >
      <RefreshCw className={cn('h-4 w-4', isRefreshing && 'animate-spin')} />
    </button>
  )
}

export default LoadingState
