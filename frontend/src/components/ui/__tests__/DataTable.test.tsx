/**
 * Tests for DataTable component
 */

import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '../../../test/utils'
import DataTable, { Column } from '../DataTable'

interface TestData {
  id: number
  name: string
  status: string
  count: number
}

const mockData: TestData[] = [
  { id: 1, name: 'Item 1', status: 'active', count: 10 },
  { id: 2, name: 'Item 2', status: 'inactive', count: 5 },
  { id: 3, name: 'Item 3', status: 'active', count: 15 },
]

const columns: Column<TestData>[] = [
  {
    key: 'id',
    header: 'ID',
    sortable: true,
  },
  {
    key: 'name',
    header: 'Name',
    sortable: true,
  },
  {
    key: 'status',
    header: 'Status',
    render: (value) => (
      <span className={value === 'active' ? 'text-green-600' : 'text-red-600'}>
        {value}
      </span>
    ),
  },
  {
    key: 'count',
    header: 'Count',
    sortable: true,
  },
]

describe('DataTable', () => {
  it('renders table with data', () => {
    render(<DataTable data={mockData} columns={columns} />)
    
    // Check headers
    expect(screen.getByText('ID')).toBeInTheDocument()
    expect(screen.getByText('Name')).toBeInTheDocument()
    expect(screen.getByText('Status')).toBeInTheDocument()
    expect(screen.getByText('Count')).toBeInTheDocument()
    
    // Check data rows
    expect(screen.getByText('Item 1')).toBeInTheDocument()
    expect(screen.getByText('Item 2')).toBeInTheDocument()
    expect(screen.getByText('Item 3')).toBeInTheDocument()
  })

  it('renders custom cell content', () => {
    render(<DataTable data={mockData} columns={columns} />)
    
    // Check custom rendered status cells
    const activeStatuses = screen.getAllByText('active')
    expect(activeStatuses[0]).toHaveClass('text-green-600')
    
    const inactiveStatus = screen.getByText('inactive')
    expect(inactiveStatus).toHaveClass('text-red-600')
  })

  it('handles empty data', () => {
    render(<DataTable data={[]} columns={columns} />)
    
    expect(screen.getByText('No data available')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    render(<DataTable data={mockData} columns={columns} loading />)
    
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('handles search functionality', () => {
    const onSearch = vi.fn()
    render(
      <DataTable 
        data={mockData} 
        columns={columns} 
        searchable 
        onSearch={onSearch}
        searchPlaceholder="Search items..."
      />
    )
    
    const searchInput = screen.getByPlaceholderText('Search items...')
    expect(searchInput).toBeInTheDocument()
    
    fireEvent.change(searchInput, { target: { value: 'Item 1' } })
    expect(onSearch).toHaveBeenCalledWith('Item 1')
  })

  it('handles sorting', () => {
    const onSort = vi.fn()
    render(
      <DataTable 
        data={mockData} 
        columns={columns} 
        onSort={onSort}
      />
    )
    
    // Click on sortable header
    const nameHeader = screen.getByText('Name')
    fireEvent.click(nameHeader)
    
    expect(onSort).toHaveBeenCalledWith('name', 'asc')
    
    // Click again for descending
    fireEvent.click(nameHeader)
    expect(onSort).toHaveBeenCalledWith('name', 'desc')
  })

  it('shows sort indicators', () => {
    render(
      <DataTable 
        data={mockData} 
        columns={columns} 
        sortBy="name"
        sortOrder="asc"
      />
    )
    
    // Should show ascending sort indicator
    const nameHeader = screen.getByText('Name').closest('th')
    expect(nameHeader?.querySelector('svg')).toBeInTheDocument()
  })

  it('handles pagination', () => {
    const onPageChange = vi.fn()
    const onSizeChange = vi.fn()
    
    render(
      <DataTable 
        data={mockData} 
        columns={columns} 
        pagination={{
          page: 1,
          size: 2,
          total: 3,
          onPageChange,
          onSizeChange,
        }}
      />
    )
    
    // Check pagination info (text is split across elements)
    expect(screen.getByText('Showing')).toBeInTheDocument()
    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('to')).toBeInTheDocument()
    expect(screen.getByText('2')).toBeInTheDocument()
    expect(screen.getByText('of')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
    expect(screen.getByText('results')).toBeInTheDocument()
  })

  it('disables non-sortable columns', () => {
    const onSort = vi.fn()
    render(
      <DataTable 
        data={mockData} 
        columns={columns} 
        onSort={onSort}
      />
    )
    
    // Status column is not sortable
    const statusHeader = screen.getByText('Status')
    fireEvent.click(statusHeader)
    
    expect(onSort).not.toHaveBeenCalled()
  })

  it('applies custom className', () => {
    render(
      <DataTable
        data={mockData}
        columns={columns}
        className="custom-table"
      />
    )

    // The className is applied to the wrapper div
    const wrapper = screen.getByRole('table').closest('.space-y-4')
    expect(wrapper).toHaveClass('custom-table')
  })

  it('renders table structure correctly', () => {
    render(<DataTable data={mockData} columns={columns} />)

    // Check table structure
    const table = screen.getByRole('table')
    expect(table).toBeInTheDocument()

    const thead = table.querySelector('thead')
    const tbody = table.querySelector('tbody')
    expect(thead).toBeInTheDocument()
    expect(tbody).toBeInTheDocument()

    // Check number of rows
    const rows = screen.getAllByRole('row')
    expect(rows).toHaveLength(4) // 1 header + 3 data rows
  })
})
