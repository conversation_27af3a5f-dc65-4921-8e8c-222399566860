/**
 * Tests for ErrorAlert component
 */

import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import ErrorAlert from '../ErrorAlert'
import { ApiClientError, ApiErrorType } from '../../../lib/api'

describe('ErrorAlert', () => {
  it('renders nothing when no error is provided', () => {
    const { container } = render(<ErrorAlert error={null} />)
    expect(container.firstChild).toBeNull()
  })

  it('renders generic error for standard Error', () => {
    const error = new Error('Something went wrong')
    
    render(<ErrorAlert error={error} />)

    expect(screen.getByText('Error')).toBeInTheDocument()
    expect(screen.getByText('Something went wrong')).toBeInTheDocument()
    expect(screen.getByText('Retry')).toBeInTheDocument()
  })

  it('renders offline error correctly', () => {
    const error = new ApiClientError(
      'Unable to connect to server',
      ApiErrorType.OFFLINE_ERROR
    )
    
    render(<ErrorAlert error={error} />)

    expect(screen.getByText('Backend Unavailable')).toBeInTheDocument()
    expect(screen.getByText('Unable to connect to the server. Please check if the backend is running.')).toBeInTheDocument()
  })

  it('renders timeout error correctly', () => {
    const error = new ApiClientError(
      'Request timed out',
      ApiErrorType.TIMEOUT_ERROR
    )
    
    render(<ErrorAlert error={error} />)

    expect(screen.getByText('Request Timeout')).toBeInTheDocument()
    expect(screen.getByText('The request took too long to complete. Please try again.')).toBeInTheDocument()
  })

  it('renders server error correctly', () => {
    const error = new ApiClientError(
      'Internal server error',
      ApiErrorType.SERVER_ERROR,
      500
    )
    
    render(<ErrorAlert error={error} />)

    expect(screen.getByText('Server Error')).toBeInTheDocument()
    expect(screen.getByText('A server error occurred. Please try again later.')).toBeInTheDocument()
  })

  it('renders client error correctly without retry button', () => {
    const error = new ApiClientError(
      'Bad request',
      ApiErrorType.CLIENT_ERROR,
      400
    )
    
    render(<ErrorAlert error={error} />)

    expect(screen.getByText('Request Error')).toBeInTheDocument()
    expect(screen.getByText('Bad request')).toBeInTheDocument()
    expect(screen.queryByText('Retry')).not.toBeInTheDocument()
  })

  it('calls onRetry when retry button is clicked', () => {
    const onRetry = vi.fn()
    const error = new ApiClientError(
      'Network error',
      ApiErrorType.NETWORK_ERROR
    )
    
    render(<ErrorAlert error={error} onRetry={onRetry} />)

    fireEvent.click(screen.getByText('Retry'))
    expect(onRetry).toHaveBeenCalledTimes(1)
  })

  it('hides retry button when showRetryButton is false', () => {
    const error = new ApiClientError(
      'Network error',
      ApiErrorType.NETWORK_ERROR
    )
    
    render(<ErrorAlert error={error} showRetryButton={false} />)

    expect(screen.queryByText('Retry')).not.toBeInTheDocument()
  })

  it('uses custom title when provided', () => {
    const error = new Error('Test error')
    
    render(<ErrorAlert error={error} title="Custom Error Title" />)

    expect(screen.getByText('Custom Error Title')).toBeInTheDocument()
    expect(screen.queryByText('Error')).not.toBeInTheDocument()
  })

  it('applies custom className', () => {
    const error = new Error('Test error')
    
    const { container } = render(
      <ErrorAlert error={error} className="custom-class" />
    )

    expect(container.firstChild).toHaveClass('custom-class')
  })
})
