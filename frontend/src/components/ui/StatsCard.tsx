/**
 * StatsCard component for displaying key metrics and statistics
 */

import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react'
import { cn } from '../../lib/utils'
import { Card, CardContent } from './Card'

export interface StatsCardProps {
  title: string
  value: string | number | React.ReactNode
  icon: LucideIcon
  iconColor?: string
  iconBgColor?: string
  change?: {
    value: number
    label: string
    type: 'increase' | 'decrease' | 'neutral'
  }
  className?: string
}

export default function StatsCard({
  title,
  value,
  icon: Icon,
  iconColor = 'text-blue-600',
  iconBgColor = 'bg-blue-100',
  change,
  className,
}: StatsCardProps) {
  const getChangeColor = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return 'text-green-600'
      case 'decrease':
        return 'text-red-600'
      case 'neutral':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  const getChangeIcon = (type: 'increase' | 'decrease' | 'neutral') => {
    switch (type) {
      case 'increase':
        return <TrendingUp className="h-4 w-4" />
      case 'decrease':
        return <TrendingDown className="h-4 w-4" />
      default:
        return null
    }
  }

  return (
    <Card className={cn('', className)}>
      <CardContent className="p-6">
        <div className="flex items-center">
          <div className={cn('p-2 rounded-lg', iconBgColor)}>
            <Icon className={cn('h-6 w-6', iconColor)} />
          </div>
          <div className="ml-4 flex-1">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
          </div>
        </div>
        
        {change && (
          <div className="mt-4 flex items-center text-sm">
            <div className={cn('flex items-center', getChangeColor(change.type))}>
              {getChangeIcon(change.type)}
              <span className="ml-1 font-medium">
                {change.type === 'increase' ? '+' : change.type === 'decrease' ? '-' : ''}
                {Math.abs(change.value)}
                {typeof change.value === 'number' && change.value % 1 !== 0 ? '%' : ''}
              </span>
            </div>
            <span className="text-gray-600 ml-1">{change.label}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
