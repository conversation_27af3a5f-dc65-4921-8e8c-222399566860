/**
 * Reusable error alert component for displaying different types of errors
 */

import { <PERSON>ert<PERSON>riangle, Wifi, WifiOff, Server, Clock, RefreshCw } from 'lucide-react'
import { ApiClientError, ApiErrorType } from '../../lib/api'
import Button from './Button'

interface ErrorAlertProps {
  error: Error | ApiClientError | null
  onRetry?: () => void
  className?: string
  showRetryButton?: boolean
  title?: string
}

export default function ErrorAlert({ 
  error, 
  onRetry, 
  className = '', 
  showRetryButton = true,
  title 
}: ErrorAlertProps) {
  if (!error) return null

  const getErrorInfo = (error: Error | ApiClientError) => {
    if (error instanceof ApiClientError) {
      switch (error.type) {
        case ApiErrorType.OFFLINE_ERROR:
          return {
            icon: WifiOff,
            iconColor: 'text-orange-600',
            bgColor: 'bg-orange-50',
            borderColor: 'border-orange-200',
            textColor: 'text-orange-800',
            title: 'Backend Unavailable',
            message: 'Unable to connect to the server. Please check if the backend is running.',
            showRetry: true
          }
        case ApiErrorType.TIMEOUT_ERROR:
          return {
            icon: Clock,
            iconColor: 'text-yellow-600',
            bgColor: 'bg-yellow-50',
            borderColor: 'border-yellow-200',
            textColor: 'text-yellow-800',
            title: 'Request Timeout',
            message: 'The request took too long to complete. Please try again.',
            showRetry: true
          }
        case ApiErrorType.SERVER_ERROR:
          return {
            icon: Server,
            iconColor: 'text-red-600',
            bgColor: 'bg-red-50',
            borderColor: 'border-red-200',
            textColor: 'text-red-800',
            title: 'Server Error',
            message: 'A server error occurred. Please try again later.',
            showRetry: true
          }
        case ApiErrorType.NETWORK_ERROR:
          return {
            icon: WifiOff,
            iconColor: 'text-red-600',
            bgColor: 'bg-red-50',
            borderColor: 'border-red-200',
            textColor: 'text-red-800',
            title: 'Network Error',
            message: 'A network error occurred. Please check your connection.',
            showRetry: true
          }
        case ApiErrorType.CLIENT_ERROR:
          return {
            icon: AlertTriangle,
            iconColor: 'text-red-600',
            bgColor: 'bg-red-50',
            borderColor: 'border-red-200',
            textColor: 'text-red-800',
            title: 'Request Error',
            message: error.message || 'An error occurred with your request.',
            showRetry: false
          }
        default:
          return {
            icon: AlertTriangle,
            iconColor: 'text-red-600',
            bgColor: 'bg-red-50',
            borderColor: 'border-red-200',
            textColor: 'text-red-800',
            title: 'Error',
            message: error.message || 'An unexpected error occurred.',
            showRetry: true
          }
      }
    }

    // Generic error
    return {
      icon: AlertTriangle,
      iconColor: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      textColor: 'text-red-800',
      title: 'Error',
      message: error.message || 'An unexpected error occurred.',
      showRetry: true
    }
  }

  const errorInfo = getErrorInfo(error)
  const Icon = errorInfo.icon

  return (
    <div className={`${errorInfo.bgColor} border ${errorInfo.borderColor} rounded-lg p-4 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start">
          <Icon className={`h-5 w-5 ${errorInfo.iconColor} mr-3 mt-0.5 flex-shrink-0`} />
          <div className="flex-1">
            <h3 className={`text-sm font-medium ${errorInfo.textColor}`}>
              {title || errorInfo.title}
            </h3>
            <p className={`text-sm ${errorInfo.textColor} mt-1`}>
              {errorInfo.message}
            </p>
          </div>
        </div>
        
        {showRetryButton && errorInfo.showRetry && onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="ml-3 flex-shrink-0"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Retry
          </Button>
        )}
      </div>
    </div>
  )
}
