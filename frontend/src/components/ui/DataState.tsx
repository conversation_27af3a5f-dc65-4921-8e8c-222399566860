/**
 * Component for displaying different data states (loading, error, empty, unavailable)
 */

import { Loader2, AlertTriangle, Database, WifiOff } from 'lucide-react'
import { ApiClientError, ApiErrorType } from '../../lib/api'

interface DataStateProps {
  loading?: boolean
  error?: Error | ApiClientError | null
  data?: any
  emptyMessage?: string
  className?: string
  children?: React.ReactNode
}

export default function DataState({ 
  loading, 
  error, 
  data, 
  emptyMessage = 'No data available',
  className = '',
  children 
}: DataStateProps) {
  // Loading state
  if (loading) {
    return (
      <div className={`flex items-center justify-center py-8 ${className}`}>
        <div className="flex items-center text-gray-500">
          <Loader2 className="h-5 w-5 animate-spin mr-2" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    const isOfflineError = error instanceof ApiClientError && 
      (error.type === ApiErrorType.OFFLINE_ERROR || error.type === ApiErrorType.TIMEOUT_ERROR)
    
    return (
      <div className={`flex items-center justify-center py-8 ${className}`}>
        <div className="flex items-center text-gray-500">
          {isOfflineError ? (
            <WifiOff className="h-5 w-5 mr-2" />
          ) : (
            <AlertTriangle className="h-5 w-5 mr-2" />
          )}
          <span>
            {isOfflineError ? 'Service unavailable' : 'Failed to load data'}
          </span>
        </div>
      </div>
    )
  }

  // Empty state
  if (!data || (Array.isArray(data) && data.length === 0)) {
    return (
      <div className={`flex items-center justify-center py-8 ${className}`}>
        <div className="flex items-center text-gray-500">
          <Database className="h-5 w-5 mr-2" />
          <span>{emptyMessage}</span>
        </div>
      </div>
    )
  }

  // Render children when data is available
  return <>{children}</>
}

/**
 * Hook to get display value for data that might be unavailable
 */
export function useDataValue(
  value: any, 
  loading: boolean, 
  error: Error | ApiClientError | null,
  fallback: string = 'N/A'
): string {
  if (loading) return '...'
  
  if (error) {
    const isOfflineError = error instanceof ApiClientError && 
      (error.type === ApiErrorType.OFFLINE_ERROR || error.type === ApiErrorType.TIMEOUT_ERROR)
    return isOfflineError ? 'N/A' : '—'
  }
  
  if (value === null || value === undefined) return fallback
  
  return String(value)
}

/**
 * Component for displaying a single data value with proper error handling
 */
interface DataValueProps {
  value: any
  loading?: boolean
  error?: Error | ApiClientError | null
  fallback?: string
  formatter?: (value: any) => string
  className?: string
}

export function DataValue({ 
  value, 
  loading, 
  error, 
  fallback = 'N/A',
  formatter,
  className = ''
}: DataValueProps) {
  const displayValue = useDataValue(value, loading || false, error, fallback)
  const formattedValue = formatter && displayValue !== '...' && displayValue !== 'N/A' && displayValue !== '—' 
    ? formatter(value) 
    : displayValue

  const getValueClass = () => {
    if (loading) return 'text-gray-500'
    if (error) {
      const isOfflineError = error instanceof ApiClientError && 
        (error.type === ApiErrorType.OFFLINE_ERROR || error.type === ApiErrorType.TIMEOUT_ERROR)
      return isOfflineError ? 'text-orange-600' : 'text-red-600'
    }
    return ''
  }

  return (
    <span className={`${getValueClass()} ${className}`}>
      {formattedValue}
    </span>
  )
}
