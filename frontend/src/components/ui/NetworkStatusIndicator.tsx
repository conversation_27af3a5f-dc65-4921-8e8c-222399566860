/**
 * Network status indicator component
 */

import { Wifi, WifiOff, Server, AlertCircle, CheckCircle } from 'lucide-react'
import { useBackendConnectivity } from '../../hooks/useNetworkStatus'
import { cn } from '../../lib/utils'

interface NetworkStatusIndicatorProps {
  className?: string
  showText?: boolean
  compact?: boolean
}

export default function NetworkStatusIndicator({ 
  className = '', 
  showText = true,
  compact = false 
}: NetworkStatusIndicatorProps) {
  const { 
    isBackendOnline, 
    isChecking, 
    networkStatus,
    checkBackendConnectivity 
  } = useBackendConnectivity()

  const getStatusInfo = () => {
    if (!networkStatus.isOnline) {
      return {
        icon: WifiOff,
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        text: 'No Internet',
        description: 'Check your network connection'
      }
    }

    if (isChecking) {
      return {
        icon: Server,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        text: 'Checking...',
        description: 'Testing backend connectivity'
      }
    }

    if (isBackendOnline === false) {
      return {
        icon: AlertCircle,
        color: 'text-orange-600',
        bgColor: 'bg-orange-100',
        text: 'Backend Offline',
        description: 'Backend service is unavailable'
      }
    }

    if (isBackendOnline === true) {
      return {
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        text: 'Connected',
        description: 'All services operational'
      }
    }

    // Unknown state
    return {
      icon: Server,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      text: 'Unknown',
      description: 'Connection status unknown'
    }
  }

  const statusInfo = getStatusInfo()
  const Icon = statusInfo.icon

  if (compact) {
    return (
      <div 
        className={cn('flex items-center cursor-pointer', className)}
        onClick={checkBackendConnectivity}
        title={statusInfo.description}
      >
        <div className={cn('p-1 rounded-full', statusInfo.bgColor)}>
          <Icon className={cn('h-3 w-3', statusInfo.color)} />
        </div>
        {showText && (
          <span className={cn('ml-1 text-xs', statusInfo.color)}>
            {statusInfo.text}
          </span>
        )}
      </div>
    )
  }

  return (
    <div 
      className={cn(
        'flex items-center p-2 rounded-lg border cursor-pointer transition-colors hover:bg-gray-50',
        statusInfo.bgColor,
        className
      )}
      onClick={checkBackendConnectivity}
    >
      <Icon className={cn('h-4 w-4 mr-2', statusInfo.color)} />
      {showText && (
        <div className="flex-1">
          <div className={cn('text-sm font-medium', statusInfo.color)}>
            {statusInfo.text}
          </div>
          <div className="text-xs text-gray-600">
            {statusInfo.description}
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Simple status dot component
 */
export function NetworkStatusDot({ className = '' }: { className?: string }) {
  const { isBackendOnline, networkStatus } = useBackendConnectivity()

  const getStatusColor = () => {
    if (!networkStatus.isOnline) return 'bg-red-500'
    if (isBackendOnline === false) return 'bg-orange-500'
    if (isBackendOnline === true) return 'bg-green-500'
    return 'bg-gray-500'
  }

  const getStatusText = () => {
    if (!networkStatus.isOnline) return 'No internet connection'
    if (isBackendOnline === false) return 'Backend service unavailable'
    if (isBackendOnline === true) return 'All services operational'
    return 'Connection status unknown'
  }

  return (
    <div 
      className={cn('flex items-center', className)}
      title={getStatusText()}
    >
      <div className={cn('w-2 h-2 rounded-full', getStatusColor())} />
    </div>
  )
}
