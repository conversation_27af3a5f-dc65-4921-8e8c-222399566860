/**
 * Reusable Button component with variants and sizes
 */

import React, { forwardRef } from 'react'
import { cn } from '../../lib/utils'

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  loading?: boolean
  asChild?: boolean
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', loading, children, disabled, asChild, ...props }, ref) => {
    const baseClasses = cn(
      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
      {
        // Variants
        'bg-blue-600 text-white hover:bg-blue-700': variant === 'default',
        'bg-red-600 text-white hover:bg-red-700': variant === 'destructive',
        'border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900': variant === 'outline',
        'bg-gray-100 text-gray-900 hover:bg-gray-200': variant === 'secondary',
        'hover:bg-gray-100 hover:text-gray-900': variant === 'ghost',
        'text-blue-600 underline-offset-4 hover:underline': variant === 'link',

        // Sizes
        'h-10 px-4 py-2': size === 'default',
        'h-9 rounded-md px-3': size === 'sm',
        'h-11 rounded-md px-8': size === 'lg',
        'h-10 w-10': size === 'icon',
      },
      className
    )

    if (asChild) {
      // When asChild is true, we expect children to be a single React element
      // that we'll clone with our classes
      const child = children as React.ReactElement
      return React.cloneElement(child, {
        ...props,
        className: cn(baseClasses, (child.props as any)?.className),
      })
    }

    return (
      <button
        className={baseClasses}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}
        {children}
      </button>
    )
  }
)

Button.displayName = 'Button'

export { Button }
export default Button
