/**
 * Transition components for smooth animations
 */

import React, { useEffect, useState } from 'react'
import { cn } from '../../lib/utils'

interface FadeInProps {
  children: React.ReactNode
  delay?: number
  duration?: number
  className?: string
  appear?: boolean
}

export function FadeIn({ 
  children, 
  delay = 0, 
  duration = 300, 
  className,
  appear = true 
}: FadeInProps) {
  const [isVisible, setIsVisible] = useState(!appear)

  useEffect(() => {
    if (appear) {
      const timer = setTimeout(() => setIsVisible(true), delay)
      return () => clearTimeout(timer)
    }
  }, [delay, appear])

  return (
    <div
      className={cn(
        'transition-all ease-out',
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2',
        className
      )}
      style={{
        transitionDuration: `${duration}ms`,
        transitionDelay: appear ? `${delay}ms` : '0ms'
      }}
    >
      {children}
    </div>
  )
}

interface SlideInProps {
  children: React.ReactNode
  direction?: 'left' | 'right' | 'up' | 'down'
  delay?: number
  duration?: number
  className?: string
  appear?: boolean
}

export function SlideIn({ 
  children, 
  direction = 'up', 
  delay = 0, 
  duration = 300, 
  className,
  appear = true 
}: SlideInProps) {
  const [isVisible, setIsVisible] = useState(!appear)

  useEffect(() => {
    if (appear) {
      const timer = setTimeout(() => setIsVisible(true), delay)
      return () => clearTimeout(timer)
    }
  }, [delay, appear])

  const getTransform = () => {
    if (isVisible) return 'translate-x-0 translate-y-0'
    
    switch (direction) {
      case 'left':
        return '-translate-x-4 translate-y-0'
      case 'right':
        return 'translate-x-4 translate-y-0'
      case 'up':
        return 'translate-x-0 -translate-y-4'
      case 'down':
        return 'translate-x-0 translate-y-4'
      default:
        return 'translate-x-0 translate-y-4'
    }
  }

  return (
    <div
      className={cn(
        'transition-all ease-out',
        isVisible ? 'opacity-100' : 'opacity-0',
        getTransform(),
        className
      )}
      style={{
        transitionDuration: `${duration}ms`,
        transitionDelay: appear ? `${delay}ms` : '0ms'
      }}
    >
      {children}
    </div>
  )
}

interface StaggeredFadeInProps {
  children: React.ReactNode[]
  staggerDelay?: number
  initialDelay?: number
  duration?: number
  className?: string
}

export function StaggeredFadeIn({ 
  children, 
  staggerDelay = 100, 
  initialDelay = 0, 
  duration = 300,
  className 
}: StaggeredFadeInProps) {
  return (
    <>
      {React.Children.map(children, (child, index) => (
        <FadeIn
          key={index}
          delay={initialDelay + (index * staggerDelay)}
          duration={duration}
          className={className}
        >
          {child}
        </FadeIn>
      ))}
    </>
  )
}

interface ScaleInProps {
  children: React.ReactNode
  delay?: number
  duration?: number
  className?: string
  appear?: boolean
}

export function ScaleIn({ 
  children, 
  delay = 0, 
  duration = 300, 
  className,
  appear = true 
}: ScaleInProps) {
  const [isVisible, setIsVisible] = useState(!appear)

  useEffect(() => {
    if (appear) {
      const timer = setTimeout(() => setIsVisible(true), delay)
      return () => clearTimeout(timer)
    }
  }, [delay, appear])

  return (
    <div
      className={cn(
        'transition-all ease-out',
        isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95',
        className
      )}
      style={{
        transitionDuration: `${duration}ms`,
        transitionDelay: appear ? `${delay}ms` : '0ms'
      }}
    >
      {children}
    </div>
  )
}

// Utility component for smooth height transitions
interface CollapseProps {
  isOpen: boolean
  children: React.ReactNode
  duration?: number
  className?: string
}

export function Collapse({ 
  isOpen, 
  children, 
  duration = 300, 
  className 
}: CollapseProps) {
  return (
    <div
      className={cn(
        'overflow-hidden transition-all ease-out',
        className
      )}
      style={{
        transitionDuration: `${duration}ms`,
        maxHeight: isOpen ? '1000px' : '0px'
      }}
    >
      {children}
    </div>
  )
}

export default FadeIn
