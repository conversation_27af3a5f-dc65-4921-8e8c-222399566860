/**
 * Integration tests for PicksList page with API
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor, fireEvent } from '../../../test/utils'
import PicksList from '../PicksList'
import { server } from '../../../test/mocks/server'
import { http, HttpResponse } from 'msw'

describe('PicksList Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('loads and displays picks from API', async () => {
    render(<PicksList />)

    // Check that picks are displayed (using mock data)
    expect(screen.getByText('PICK-20240314-ABC123')).toBeInTheDocument()
    expect(screen.getByText('PICK-20240314-DEF456')).toBeInTheDocument()

    // Check table headers
    expect(screen.getByText('Pick ID')).toBeInTheDocument()
    expect(screen.getByText('Item SKU')).toBeInTheDocument()
    expect(screen.getByText('Operator')).toBeInTheDocument()
    expect(screen.getByText('Status')).toBeInTheDocument()
  })

  it('handles search functionality', async () => {
    render(<PicksList />)

    // Find search input
    const searchInput = screen.getByPlaceholderText('Search picks...')
    expect(searchInput).toBeInTheDocument()

    // Type in search
    fireEvent.change(searchInput, { target: { value: 'ABC123' } })

    // The search should filter results
    await waitFor(() => {
      expect(searchInput).toHaveValue('ABC123')
    })
  })

  it('handles pagination', async () => {
    render(<PicksList />)

    // Check pagination info (text is split across elements)
    expect(screen.getByText('Showing')).toBeInTheDocument()
    expect(screen.getByText('1')).toBeInTheDocument()
    expect(screen.getByText('to')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument() // Total items in mock data
    expect(screen.getByText('of')).toBeInTheDocument()
    expect(screen.getByText('results')).toBeInTheDocument()
  })

  it('displays picks data correctly', async () => {
    render(<PicksList />)

    // Check that mock data is displayed
    expect(screen.getByText('PICK-20240314-ABC123')).toBeInTheDocument()
    expect(screen.getByText('SKU-WIDGET-001')).toBeInTheDocument()
    expect(screen.getByText('OP-001')).toBeInTheDocument()
  })

  it('displays correct status badges', async () => {
    render(<PicksList />)

    // Check for status badges
    expect(screen.getByText('COMPLETED')).toBeInTheDocument()
    expect(screen.getByText('PENDING')).toBeInTheDocument()
    expect(screen.getByText('SHORT PICKED')).toBeInTheDocument()
  })

  it('handles sorting', async () => {
    render(<PicksList />)

    // Click on sortable header
    const pickIdHeader = screen.getByText('Pick ID')
    fireEvent.click(pickIdHeader)

    // Should show sort indicator
    const headerElement = pickIdHeader.closest('th')
    expect(headerElement?.querySelector('svg')).toBeInTheDocument()
  })

  it('renders page structure correctly', async () => {
    render(<PicksList />)

    // Check page title and description
    expect(screen.getByText('Picks')).toBeInTheDocument()
    expect(screen.getByText('Manage warehouse pick operations')).toBeInTheDocument()

    // Check create button
    expect(screen.getByText('Create Pick')).toBeInTheDocument()

    // Check card title
    expect(screen.getByText('All Picks')).toBeInTheDocument()
  })
})
