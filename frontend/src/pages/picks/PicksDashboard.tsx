/**
 * Picks Dashboard page with statistics and charts
 */

import { Link } from 'react-router-dom'
import { Package, Clock, CheckCircle, AlertCircle, Plus, List } from 'lucide-react'
import StatsCard from '../../components/ui/StatsCard'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Badge from '../../components/ui/Badge'
import { usePickStatistics, usePicks } from '../../hooks/use-picks'
import { formatDateTime, getStatusColor } from '../../lib/utils'

export default function PicksDashboard() {
  const { data: stats, isLoading: statsLoading } = usePickStatistics()
  const { data: recentPicksData, isLoading: picksLoading } = usePicks({ page: 1, size: 5 })

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Picks Dashboard</h1>
          <p className="text-gray-600">Monitor pick operations and performance</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link to="/picks/list">
              <List className="h-4 w-4 mr-2" />
              View All Picks
            </Link>
          </Button>
          <Button asChild>
            <Link to="/picks/create">
              <Plus className="h-4 w-4 mr-2" />
              Create Pick
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <StatsCard
          title="Total Picks"
          value={statsLoading ? "..." : stats?.total_picks?.toLocaleString() || "0"}
          icon={Package}
          iconColor="text-blue-600"
          iconBgColor="bg-blue-100"
          change={stats ? {
            value: stats.completed_picks,
            label: "completed",
            type: "neutral"
          } : undefined}
        />
        
        <StatsCard
          title="Completed"
          value={statsLoading ? "..." : stats?.completed_picks?.toLocaleString() || "0"}
          icon={CheckCircle}
          iconColor="text-green-600"
          iconBgColor="bg-green-100"
          change={stats ? {
            value: Math.round(stats.pick_accuracy || 0),
            label: "accuracy",
            type: stats.pick_accuracy >= 95 ? "increase" : "decrease"
          } : undefined}
        />
        
        <StatsCard
          title="Short Picked"
          value={statsLoading ? "..." : stats?.short_picked?.toString() || "0"}
          icon={AlertCircle}
          iconColor="text-yellow-600"
          iconBgColor="bg-yellow-100"
          change={stats ? {
            value: Math.round(((stats.short_picked || 0) / (stats.total_picks || 1)) * 100),
            label: "of total",
            type: "neutral"
          } : undefined}
        />
        
        <StatsCard
          title="Pick Accuracy"
          value={statsLoading ? "..." : stats ? `${Math.round(stats.pick_accuracy || 0)}%` : "0%"}
          icon={CheckCircle}
          iconColor="text-purple-600"
          iconBgColor="bg-purple-100"
          change={stats ? {
            value: Math.round(stats.pick_accuracy || 0),
            label: "accuracy rate",
            type: stats.pick_accuracy >= 95 ? "increase" : "decrease"
          } : undefined}
        />
        
        <StatsCard
          title="Avg Duration"
          value={statsLoading ? "..." : stats ? `${Math.round(stats.average_duration || 0)}s` : "0s"}
          icon={Clock}
          iconColor="text-indigo-600"
          iconBgColor="bg-indigo-100"
          change={stats ? {
            value: Math.round(stats.average_duration || 0),
            label: "seconds",
            type: "neutral"
          } : undefined}
        />
      </div>

      {/* Recent picks and performance charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent picks */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Picks</CardTitle>
          </CardHeader>
          <CardContent>
            {picksLoading ? (
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((item) => (
                  <div key={item} className="animate-pulse p-3 border rounded-lg">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {recentPicksData?.items?.slice(0, 5).map((pick) => (
                  <div key={pick.pick_id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-mono text-sm font-medium">{pick.pick_id}</span>
                        <Badge className={getStatusColor(pick.pick_status)}>
                          {pick.pick_status.replace('_', ' ').toUpperCase()}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {pick.item_sku} • {pick.operator_id}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Started: {formatDateTime(pick.start_time)}
                        {pick.end_time && ` • Completed: ${formatDateTime(pick.end_time)}`}
                      </div>
                    </div>
                  </div>
                )) || (
                  <p className="text-gray-500 text-center py-4">No recent picks found</p>
                )}
              </div>
            )}
            <div className="mt-4">
              <Button variant="outline" className="w-full" asChild>
                <Link to="/picks/list">View All Picks</Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Performance metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total Items Picked</span>
                <span className="text-2xl font-bold">
                  {statsLoading ? "..." : stats?.total_items_picked?.toLocaleString() || "0"}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Pick Accuracy</span>
                <span className="text-2xl font-bold">
                  {statsLoading ? "..." : stats ? `${Math.round(stats.pick_accuracy || 0)}%` : "0%"}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full"
                  style={{ width: `${Math.round(stats?.pick_accuracy || 0)}%` }}
                ></div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Avg Duration</span>
                <span className="text-2xl font-bold">
                  {statsLoading ? "..." : stats ? `${Math.round(stats.average_duration || 0)}s` : "0s"}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-purple-600 h-2 rounded-full" style={{ width: '60%' }}></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
