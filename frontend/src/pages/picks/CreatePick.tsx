/**
 * Create Pick page
 */

import { useNavigate } from 'react-router-dom'
import PickForm from '../../components/forms/PickForm'
import { ArrowLeft } from 'lucide-react'
import Button from '../../components/ui/Button'

export default function CreatePick() {
  const navigate = useNavigate()

  const handleSuccess = () => {
    navigate('/picks/list')
  }

  const handleCancel = () => {
    navigate('/picks/list')
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate('/picks/list')}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Pick</h1>
          <p className="text-gray-600">Create a new pick operation</p>
        </div>
      </div>

      {/* Form */}
      <div className="flex justify-center">
        <PickForm
          mode="create"
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  )
}
