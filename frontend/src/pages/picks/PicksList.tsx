/**
 * Picks List page with DataTable
 */

import { useState } from 'react'
import DataTable, { Column } from '../../components/ui/DataTable'
import Badge from '../../components/ui/Badge'
import Button from '../../components/ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card'
import ErrorAlert from '../../components/ui/ErrorAlert'
import { Plus, AlertTriangle } from 'lucide-react'
import { PickResponse, PickStatus } from '../../lib/types'
import { formatDateTime, getStatusColor } from '../../lib/utils'
import { usePicks } from '../../hooks/use-picks'

export default function PicksList() {
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // Fetch picks data from API
  const {
    data: picksData,
    isLoading,
    error,
    refetch
  } = usePicks({
    page,
    size: pageSize,
    search: searchTerm || undefined
  })

  const columns: Column<PickResponse>[] = [
    {
      key: 'pick_id',
      header: 'Pick ID',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'item_sku',
      header: 'Item SKU',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-mono text-sm">{value}</div>
          <div className="text-xs text-gray-500">{row.item_description}</div>
        </div>
      ),
    },
    {
      key: 'operator_id',
      header: 'Operator',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'quantity_requested',
      header: 'Qty Req/Picked',
      render: (value, row) => (
        <span className="text-sm">
          {row.quantity_picked}/{value}
        </span>
      ),
    },
    {
      key: 'location_from',
      header: 'From → To',
      render: (value, row) => (
        <span className="text-sm font-mono">
          {value} → {row.location_to || '—'}
        </span>
      ),
    },
    {
      key: 'pick_status',
      header: 'Status',
      sortable: true,
      render: (value) => (
        <Badge className={getStatusColor(value)}>
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'start_time',
      header: 'Started',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{formatDateTime(value)}</span>
      ),
    },
  ]

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Picks</h1>
            <p className="text-gray-600">Manage warehouse pick operations</p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Pick
          </Button>
        </div>
        <ErrorAlert
          error={error}
          onRetry={refetch}
          title="Failed to load picks data"
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Picks</h1>
          <p className="text-gray-600">Manage warehouse pick operations</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Pick
        </Button>
      </div>

      {/* Picks table */}
      <Card>
        <CardHeader>
          <CardTitle>All Picks</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={picksData?.items || []}
            columns={columns}
            loading={isLoading}
            searchable
            searchPlaceholder="Search picks..."
            onSearch={setSearchTerm}
            pagination={{
              page,
              size: pageSize,
              total: picksData?.total || 0,
              onPageChange: setPage,
              onSizeChange: setPageSize,
            }}
          />
        </CardContent>
      </Card>
    </div>
  )
}
