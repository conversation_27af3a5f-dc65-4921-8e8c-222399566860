/**
 * Main dashboard page showing overview of warehouse operations
 */

import React, { useState, useEffect } from 'react'
import { BarChart3, Package, Users, Truck, AlertTriangle } from 'lucide-react'
import StatsCard from '../components/ui/StatsCard'
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/Card'
import Badge from '../components/ui/Badge'
import SimpleBarChart from '../components/charts/SimpleBarChart'
import SimpleLine<PERSON>hart from '../components/charts/SimpleLineChart'
import ErrorAlert from '../components/ui/ErrorAlert'
import DataState, { DataValue } from '../components/ui/DataState'
import { SkeletonCard, SkeletonChart, SkeletonTable } from '../components/ui/Skeleton'
import { InlineLoading } from '../components/ui/LoadingState'
import { FadeIn, StaggeredFadeIn } from '../components/ui/Transitions'
import { usePickStatistics } from '../hooks/use-picks'
import { usePicks } from '../hooks/use-picks'
import { useFailedMessages } from '../hooks/use-system'
import { formatDateTime, getStatusColor } from '../lib/utils'
import { useErrorStore } from '../stores/errorStore'

export default function Dashboard() {
  const [isInitialLoad, setIsInitialLoad] = useState(true)

  // Fetch real data from API
  const { data: pickStats, isLoading: statsLoading, error: statsError, refetch: refetchStats } = usePickStatistics()
  const { data: recentPicks, isLoading: picksLoading, error: picksError, refetch: refetchPicks } = usePicks({ page: 1, size: 5 })
  const { data: failedMessages, isLoading: failedLoading, error: failedError, refetch: refetchFailed } = useFailedMessages({ page: 1, size: 3 })

  // Get API status from error store
  const isApiAvailable = useErrorStore((state) => state.isServiceAvailable())

  // Handle initial load state
  useEffect(() => {
    if (!statsLoading && !picksLoading && !failedLoading) {
      const timer = setTimeout(() => setIsInitialLoad(false), 300)
      return () => clearTimeout(timer)
    }
  }, [statsLoading, picksLoading, failedLoading])

  // Mock data for charts (will be replaced with real data when chart endpoints are available)
  const weeklyPicksData = [
    { label: 'Mon', value: 145 },
    { label: 'Tue', value: 162 },
    { label: 'Wed', value: 138 },
    { label: 'Thu', value: 175 },
    { label: 'Fri', value: 189 },
    { label: 'Sat', value: 95 },
    { label: 'Sun', value: 67 },
  ]

  const departmentData = [
    { label: 'Picking', value: 45, color: '#3B82F6' },
    { label: 'Packing', value: 32, color: '#10B981' },
    { label: 'Shipping', value: 28, color: '#F59E0B' },
    { label: 'Receiving', value: 15, color: '#EF4444' },
  ]

  // Check if we have any critical errors that should show a full error page
  const hasCriticalError = statsError && picksError && failedError

  if (hasCriticalError) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Overview of warehouse operations</p>
        </div>
        <ErrorAlert
          error={statsError}
          onRetry={refetchStats}
          title="Failed to load dashboard data"
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <FadeIn>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Overview of warehouse operations</p>
        </div>
      </FadeIn>

      {/* API Status Alert */}
      {!isApiAvailable && (
        <ErrorAlert
          error={statsError || picksError || failedError}
          onRetry={() => {
            refetchStats()
            refetchPicks()
            refetchFailed()
          }}
          title="Backend Service Unavailable"
          className="mb-6"
        />
      )}

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {isInitialLoad && statsLoading ? (
          // Show skeleton cards during initial load
          <>
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
            <SkeletonCard />
          </>
        ) : (
          // Show actual stats cards
          <>
        <StatsCard
          title="Total Picks"
          value={
            <DataValue
              value={pickStats?.total_picks}
              loading={statsLoading}
              error={statsError}
              formatter={(val) => val?.toLocaleString()}
            />
          }
          icon={Package}
          iconColor="text-blue-600"
          iconBgColor="bg-blue-100"
          change={pickStats ? {
            value: pickStats.completed_picks,
            label: "completed",
            type: "neutral"
          } : undefined}
        />

        <StatsCard
          title="Completed Picks"
          value={
            <DataValue
              value={pickStats?.completed_picks}
              loading={statsLoading}
              error={statsError}
              formatter={(val) => val?.toLocaleString()}
            />
          }
          icon={Package}
          iconColor="text-green-600"
          iconBgColor="bg-green-100"
          change={pickStats ? {
            value: Math.round(pickStats.pick_accuracy || 0),
            label: "accuracy",
            type: pickStats.pick_accuracy >= 95 ? "increase" : "decrease"
          } : undefined}
        />

        <StatsCard
          title="Short Picked"
          value={
            <DataValue
              value={pickStats?.short_picked}
              loading={statsLoading}
              error={statsError}
              formatter={(val) => val?.toLocaleString()}
            />
          }
          icon={AlertTriangle}
          iconColor="text-yellow-600"
          iconBgColor="bg-yellow-100"
          change={pickStats ? {
            value: Math.round(((pickStats.short_picked || 0) / (pickStats.total_picks || 1)) * 100),
            label: "of total",
            type: "neutral"
          } : undefined}
        />

        <StatsCard
          title="Avg Duration"
          value={
            <DataValue
              value={pickStats?.average_duration}
              loading={statsLoading}
              error={statsError}
              formatter={(val) => `${Math.round(val || 0)}s`}
            />
          }
          icon={BarChart3}
          iconColor="text-purple-600"
          iconBgColor="bg-purple-100"
          change={pickStats ? {
            value: Math.round(pickStats.average_duration || 0),
            label: "seconds",
            type: "neutral"
          } : undefined}
        />
        )}
      </div>

      {/* Recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Picks</CardTitle>
          </CardHeader>
          <CardContent>
            <DataState
              loading={picksLoading}
              error={picksError}
              data={recentPicks?.items}
              emptyMessage="No recent picks found"
            >
              <div className="space-y-4">
                {recentPicks?.items?.slice(0, 5).map((pick) => (
                  <div key={pick.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {pick.pick_id}
                      </p>
                      <p className="text-sm text-gray-600">
                        Operator: {pick.operator_id} • {pick.location_from} → {pick.location_to || '—'}
                      </p>
                    </div>
                    <Badge className={getStatusColor(pick.pick_status)}>
                      {pick.pick_status.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>
                ))}
              </div>
            </DataState>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Alerts</CardTitle>
          </CardHeader>
          <CardContent>
            <DataState
              loading={failedLoading}
              error={failedError}
              data={failedMessages?.items}
              emptyMessage="All systems operational"
            >
              <div className="space-y-4">
                {failedMessages?.items?.length > 0 ? (
                  failedMessages.items.map((message) => (
                    <div key={message.id} className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          Failed Message: {message.queue_name}
                        </p>
                        <p className="text-sm text-gray-600">
                          {message.error_message || 'Processing error detected'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDateTime(message.first_failed_at)}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        All Systems Operational
                      </p>
                      <p className="text-sm text-gray-600">
                        No failed messages detected
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </DataState>
          </CardContent>
        </Card>
      </div>

      {/* Charts section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Weekly Pick Volume</CardTitle>
          </CardHeader>
          <CardContent>
            <SimpleLineChart
              data={weeklyPicksData}
              height={250}
              width={500}
              color="#3B82F6"
              showDots={true}
              showGrid={true}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Department Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <SimpleBarChart
              data={departmentData}
              height={250}
              showValues={true}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
