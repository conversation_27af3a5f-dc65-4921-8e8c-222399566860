/**
 * Operators Dashboard page with performance metrics and activity overview
 */

import { Link } from 'react-router-dom'
import { Users, Clock, CheckCircle, AlertTriangle, Plus, List, Activity, TrendingUp } from 'lucide-react'
import StatsCard from '../../components/ui/StatsCard'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Badge from '../../components/ui/Badge'
import DataState from '../../components/ui/DataState'
import { useOperatorEvents, useOperatorSummary, useOperatorStatistics } from '../../hooks/use-operators'
import { formatDateTime, getInitials } from '../../lib/utils'
import { Department, Role } from '../../lib/types'

export default function OperatorsDashboard() {
  const { data: eventsData, isLoading: eventsLoading, error: eventsError } = useOperatorEvents({ page: 1, size: 10 })
  const { data: statsData, isLoading: statsLoading, error: statsError } = useOperatorStatistics()

  // Use real statistics data or fallback to mock data
  const stats = statsData || {
    total_events: 24,
    active_operators: 18,
    average_productivity: "87.5",
    average_safety_score: "94.2",
    total_hours_worked: "144.0",
    total_overtime_hours: "12.0",
    by_department: {
      picking: 12,
      packing: 6,
      receiving: 4,
      shipping: 2
    },
    by_shift: {
      day: 14,
      evening: 8,
      night: 2
    },
    by_role: {
      picker: 12,
      packer: 6,
      supervisor: 3,
      trainer: 3
    },
    top_performers: [
      { operator_id: 'OP-001', name: 'John Smith', productivity_score: 95.2, safety_score: 98.1 },
      { operator_id: 'OP-002', name: 'Sarah Johnson', productivity_score: 92.8, safety_score: 96.5 },
      { operator_id: 'OP-003', name: 'Mike Wilson', productivity_score: 91.3, safety_score: 97.2 }
    ]
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Operators Dashboard</h1>
          <p className="text-gray-600">Monitor operator performance and activity</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" asChild>
            <Link to="/operators/list">
              <List className="h-4 w-4 mr-2" />
              View All Operators
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to="/operators/activity">
              <Activity className="h-4 w-4 mr-2" />
              Activity Logs
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Events"
          value={stats.total_events.toString()}
          icon={Users}
          iconColor="text-blue-600"
          iconBgColor="bg-blue-100"
          change={{
            value: stats.active_operators,
            label: "active operators",
            type: "neutral"
          }}
        />

        <StatsCard
          title="Active Operators"
          value={stats.active_operators.toString()}
          icon={CheckCircle}
          iconColor="text-green-600"
          iconBgColor="bg-green-100"
          change={{
            value: Math.round((stats.active_operators / stats.total_events) * 100),
            label: "utilization",
            type: "increase"
          }}
        />
        
        <StatsCard
          title="Avg Productivity"
          value={`${Math.round(parseFloat(stats.average_productivity))}%`}
          icon={TrendingUp}
          iconColor="text-purple-600"
          iconBgColor="bg-purple-100"
          change={{
            value: Math.round(parseFloat(stats.average_productivity)),
            label: "performance score",
            type: parseFloat(stats.average_productivity) >= 85 ? "increase" : "decrease"
          }}
        />

        <StatsCard
          title="Safety Score"
          value={`${Math.round(parseFloat(stats.average_safety_score))}%`}
          icon={AlertTriangle}
          iconColor="text-yellow-600"
          iconBgColor="bg-yellow-100"
          change={{
            value: Math.round(parseFloat(stats.average_safety_score)),
            label: "compliance rate",
            type: parseFloat(stats.average_safety_score) >= 90 ? "increase" : "decrease"
          }}
        />
      </div>

      {/* Department and shift breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Department breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Operators by Department</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(stats.by_department).map(([dept, count]) => (
                <div key={dept} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium capitalize">{dept}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{count} operators</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="h-2 bg-blue-500 rounded-full"
                        style={{ width: `${(count / stats.active_operators) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Shift breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Operators by Shift</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(stats.by_shift).map(([shift, count]) => (
                <div key={shift} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium capitalize">{shift}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">{count} operators</span>
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="h-2 bg-green-500 rounded-full"
                        style={{ width: `${(count / stats.active_operators) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent operator activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Operator Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <DataState
            loading={eventsLoading}
            error={eventsError}
            data={eventsData?.events}
            emptyMessage="No recent operator activity found"
          >
            <div className="space-y-4">
              {eventsData?.events?.slice(0, 8).map((event) => (
                <div key={event.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-700">
                        {getInitials(event.operator_id)}
                      </span>
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">{event.operator_id}</span>
                        <Badge variant="outline">
                          {event.department?.replace('_', ' ').toUpperCase() || 'N/A'}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        {event.activity_type?.replace('_', ' ').toLowerCase() || 'Activity'} • {formatDateTime(event.timestamp)}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    {event.productivity_score && (
                      <div className="text-sm font-medium">
                        {event.productivity_score}% productivity
                      </div>
                    )}
                    {event.safety_score && (
                      <div className="text-xs text-gray-500">
                        {event.safety_score}% safety
                      </div>
                    )}
                  </div>
                </div>
              )) || (
                <div className="text-center py-8 text-gray-500">
                  No recent operator activity
                </div>
              )}
            </div>
          </DataState>
        </CardContent>
      </Card>
    </div>
  )
}
