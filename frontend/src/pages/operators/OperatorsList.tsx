/**
 * Operators List page
 */

import { useState } from 'react'
import { Link } from 'react-router-dom'
import DataTable, { Column } from '../../components/ui/DataTable'
import Badge from '../../components/ui/Badge'
import Button from '../../components/ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card'
import { Plus, User, AlertTriangle } from 'lucide-react'
import { OperatorSummary, Department, Role } from '../../lib/types'
import { formatDateTime, getInitials } from '../../lib/utils'
import { useOperatorEvents } from '../../hooks/use-operators'

export default function OperatorsList() {
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // Since there's no direct operators endpoint, we'll use mock data for now
  // In a real implementation, you would create an operators endpoint or derive this from operator events
  const mockOperators: OperatorSummary[] = [
    {
      operator_id: 'OP-001',
      employee_id: 'EMP-12345678',
      full_name: '<PERSON>',
      department: Department.PICKING,
      role: Role.PICKER,
      current_productivity: 95,
      current_safety_score: 98,
      total_tasks_completed: 1250,
      total_hours_worked: 160.5,
      average_performance: {
        picks_per_hour: 24.5,
        accuracy_rate: 98.2,
      },
    },
    {
      operator_id: 'OP-002',
      employee_id: 'EMP-87654321',
      full_name: 'Sarah Johnson',
      department: Department.PACKING,
      role: Role.PACKER,
      current_productivity: 88,
      current_safety_score: 100,
      total_tasks_completed: 980,
      total_hours_worked: 152.0,
      average_performance: {
        packages_per_hour: 18.3,
        accuracy_rate: 99.1,
      },
    },
    {
      operator_id: 'OP-003',
      employee_id: 'EMP-11223344',
      full_name: 'Mike Wilson',
      department: Department.PICKING,
      role: Role.SUPERVISOR,
      current_productivity: 92,
      current_safety_score: 96,
      total_tasks_completed: 2100,
      total_hours_worked: 168.0,
      average_performance: {
        team_efficiency: 94.5,
        tasks_supervised: 45,
      },
    },
  ]

  // Filter mock data based on search term
  const filteredOperators = mockOperators.filter(operator =>
    operator.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    operator.operator_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    operator.employee_id.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Simulate loading state
  const isLoading = false
  const error = null

  const columns: Column<OperatorSummary>[] = [
    {
      key: 'operator_id',
      header: 'Operator',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-blue-600">
              {getInitials(row.full_name)}
            </span>
          </div>
          <div>
            <div className="font-medium text-gray-900">{row.full_name}</div>
            <div className="text-sm text-gray-500 font-mono">{value}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'employee_id',
      header: 'Employee ID',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'department',
      header: 'Department',
      sortable: true,
      render: (value) => (
        <Badge variant="secondary">
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'role',
      header: 'Role',
      sortable: true,
      render: (value) => (
        <Badge variant="outline">
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'current_productivity',
      header: 'Productivity',
      sortable: true,
      render: (value) => (
        <div className="flex items-center space-x-2">
          <div className="w-12 bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${
                value >= 90 ? 'bg-green-600' : 
                value >= 70 ? 'bg-yellow-600' : 'bg-red-600'
              }`}
              style={{ width: `${value}%` }}
            ></div>
          </div>
          <span className="text-sm font-medium">{value}%</span>
        </div>
      ),
    },
    {
      key: 'current_safety_score',
      header: 'Safety Score',
      sortable: true,
      render: (value) => (
        <div className="flex items-center space-x-2">
          <div className="w-12 bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${
                value >= 95 ? 'bg-green-600' : 
                value >= 85 ? 'bg-yellow-600' : 'bg-red-600'
              }`}
              style={{ width: `${value}%` }}
            ></div>
          </div>
          <span className="text-sm font-medium">{value}%</span>
        </div>
      ),
    },
    {
      key: 'total_tasks_completed',
      header: 'Tasks Completed',
      sortable: true,
      render: (value) => (
        <span className="text-sm font-medium">{value.toLocaleString()}</span>
      ),
    },
    {
      key: 'total_hours_worked',
      header: 'Hours Worked',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{value.toFixed(1)}h</span>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Operators</h1>
          <p className="text-gray-600">Manage warehouse operators and performance</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Operator
        </Button>
      </div>

      {/* Quick stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <User className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium text-gray-600">Total Operators</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">{mockOperators.length}</p>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm font-medium text-gray-600">Active Now</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {mockOperators.filter(op => (op.current_productivity || 0) > 0).length}
          </p>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-sm font-medium text-gray-600">Avg Productivity</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {Math.round(mockOperators.reduce((acc, op) => acc + (op.current_productivity || 0), 0) / mockOperators.length)}%
          </p>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm font-medium text-gray-600">Avg Safety Score</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {Math.round(mockOperators.reduce((acc, op) => acc + (op.current_safety_score || 0), 0) / mockOperators.length)}%
          </p>
        </div>
      </div>

      {/* Operators table */}
      <Card>
        <CardHeader>
          <CardTitle>All Operators</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredOperators}
            columns={columns}
            loading={isLoading}
            searchable
            searchPlaceholder="Search operators..."
            onSearch={setSearchTerm}
            pagination={{
              page,
              size: pageSize,
              total: filteredOperators.length,
              onPageChange: setPage,
              onSizeChange: setPageSize,
            }}
          />
        </CardContent>
      </Card>
    </div>
  )
}
