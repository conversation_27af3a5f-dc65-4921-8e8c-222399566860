/**
 * Vehicles List page
 */

import { useState } from 'react'
import DataTable, { Column } from '../../components/ui/DataTable'
import Badge from '../../components/ui/Badge'
import Button from '../../components/ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card'
import { Plus, Truck, Battery, MapPin, AlertTriangle } from 'lucide-react'
import { VehicleMovementResponse, VehicleType, MovementType } from '../../lib/types'
import { formatDateTime, formatDistance } from '../../lib/utils'
import { useVehicleMovements } from '../../hooks/use-vehicles'

export default function VehiclesList() {
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // Fetch vehicle movements data from API
  const {
    data: vehicleData,
    isLoading,
    error,
    refetch
  } = useVehicleMovements({
    page,
    size: pageSize,
    search: searchTerm || undefined
  })

  const columns: Column<VehicleMovementResponse>[] = [
    {
      key: 'vehicle_id',
      header: 'Vehicle',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <Truck className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <div className="font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">
              {row.vehicle_type.replace('_', ' ').toUpperCase()}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'operator_id',
      header: 'Operator',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'movement_type',
      header: 'Activity',
      sortable: true,
      render: (value) => (
        <Badge variant={
          value === MovementType.PICK ? 'default' :
          value === MovementType.PUTAWAY ? 'secondary' :
          value === MovementType.REPLENISHMENT ? 'outline' : 'secondary'
        }>
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'start_location',
      header: 'Route',
      render: (value, row) => (
        <div className="flex items-center space-x-1 text-sm">
          <span className="font-mono">{value}</span>
          <span>→</span>
          <span className="font-mono">{row.end_location || '—'}</span>
        </div>
      ),
    },
    {
      key: 'distance_meters',
      header: 'Distance',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{value ? formatDistance(value) : '—'}</span>
      ),
    },
    {
      key: 'battery_level_end',
      header: 'Battery',
      sortable: true,
      render: (value, row) => {
        const level = value || row.battery_level_start || 0
        return (
          <div className="flex items-center space-x-2">
            <Battery className={`h-4 w-4 ${
              level > 50 ? 'text-green-600' : 
              level > 20 ? 'text-yellow-600' : 'text-red-600'
            }`} />
            <span className="text-sm font-medium">{level}%</span>
          </div>
        )
      },
    },
    {
      key: 'route_efficiency',
      header: 'Efficiency',
      sortable: true,
      render: (value) => (
        <div className="flex items-center space-x-2">
          <div className="w-12 bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${
                (value || 0) >= 0.9 ? 'bg-green-600' : 
                (value || 0) >= 0.7 ? 'bg-yellow-600' : 'bg-red-600'
              }`}
              style={{ width: `${((value || 0) * 100)}%` }}
            ></div>
          </div>
          <span className="text-sm font-medium">{((value || 0) * 100).toFixed(1)}%</span>
        </div>
      ),
    },
    {
      key: 'start_time',
      header: 'Started',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{formatDateTime(value)}</span>
      ),
    },
  ]

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Vehicles</h1>
            <p className="text-gray-600">Monitor vehicle movements and performance</p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Vehicle
          </Button>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
              <span className="text-red-800">Failed to load vehicle data. Please try again.</span>
            </div>
            <Button variant="outline" onClick={() => refetch()}>
              Retry
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const vehicles = vehicleData?.items || []

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vehicles</h1>
          <p className="text-gray-600">Monitor vehicle movements and efficiency</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Movement
        </Button>
      </div>

      {/* Quick stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <Truck className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium text-gray-600">Active Vehicles</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {isLoading ? "..." : vehicleData?.total || 0}
          </p>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <MapPin className="h-5 w-5 text-green-600" />
            <span className="text-sm font-medium text-gray-600">Total Distance</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {isLoading ? "..." : formatDistance(vehicles.reduce((acc, v) => acc + (v.distance_meters || 0), 0))}
          </p>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <Battery className="h-5 w-5 text-yellow-600" />
            <span className="text-sm font-medium text-gray-600">Avg Battery</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {isLoading ? "..." : vehicles.length > 0 ?
              Math.round(vehicles.reduce((acc, v) => acc + (v.battery_level_end || v.battery_level_start || 0), 0) / vehicles.length) + "%" :
              "0%"}
          </p>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span className="text-sm font-medium text-gray-600">Avg Efficiency</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {isLoading ? "..." : vehicles.length > 0 ?
              Math.round(vehicles.reduce((acc, v) => acc + (v.route_efficiency || 0), 0) / vehicles.length * 100) + "%" :
              "0%"}
          </p>
        </div>
      </div>

      {/* Vehicles table */}
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Movements</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={vehicles}
            columns={columns}
            loading={isLoading}
            searchable
            searchPlaceholder="Search vehicles..."
            onSearch={setSearchTerm}
            pagination={{
              page,
              size: pageSize,
              total: vehicleData?.total || 0,
              onPageChange: setPage,
              onSizeChange: setPageSize,
            }}
          />
        </CardContent>
      </Card>
    </div>
  )
}
