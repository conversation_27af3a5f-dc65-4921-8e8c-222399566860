/**
 * Inventory Dashboard page with real API integration
 */

import { useState } from 'react'
import { Link } from 'react-router-dom'
import DataTable, { Column } from '../../components/ui/DataTable'
import Badge from '../../components/ui/Badge'
import Button from '../../components/ui/Button'
import StatsCard from '../../components/ui/StatsCard'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card'
import DataState from '../../components/ui/DataState'
import { Plus, Package, TrendingUp, TrendingDown, Archive } from 'lucide-react'
import { InventoryTransactionResponse, TransactionType } from '../../lib/types'
import { formatDateTime } from '../../lib/utils'
import { useInventoryTransactions, useInventoryStatistics } from '../../hooks/use-inventory'

export default function InventoryDashboard() {
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // Fetch real data from API
  const { data: transactionsData, isLoading, error } = useInventoryTransactions({ page, size: pageSize })
  const { data: statsData, isLoading: statsLoading, error: statsError } = useInventoryStatistics()

  // Use real statistics data or calculate from transaction data as fallback
  const transactions = transactionsData?.items || []
  const totalTransactions = statsData?.total_transactions || transactionsData?.total || 0
  const receipts = statsData?.by_transaction_type?.receipt || transactions.filter(t => t.transaction_type === TransactionType.RECEIPT).length
  const picks = statsData?.by_transaction_type?.pick || transactions.filter(t => t.transaction_type === TransactionType.PICK).length
  const adjustments = statsData?.by_transaction_type?.adjustment || transactions.filter(t => t.transaction_type === TransactionType.ADJUSTMENT).length

  // Filter transactions based on search term
  const filteredTransactions = transactions.filter(transaction =>
    transaction.transaction_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.item_sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.transaction_type.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const columns: Column<InventoryTransactionResponse>[] = [
    {
      key: 'transaction_id',
      header: 'Transaction ID',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'item_sku',
      header: 'Item',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-mono text-sm">{value}</div>
          <div className="text-xs text-gray-500">{row.item_description}</div>
        </div>
      ),
    },
    {
      key: 'transaction_type',
      header: 'Type',
      sortable: true,
      render: (value) => (
        <Badge variant={
          value === TransactionType.RECEIPT ? 'success' :
          value === TransactionType.PICK ? 'default' :
          value === TransactionType.ADJUSTMENT ? 'warning' :
          value === TransactionType.DAMAGE ? 'destructive' : 'secondary'
        }>
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'quantity',
      header: 'Quantity',
      sortable: true,
      render: (value) => (
        <div className={`flex items-center space-x-1 ${
          value > 0 ? 'text-green-600' : 'text-red-600'
        }`}>
          {value > 0 ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
          <span className="font-medium">{Math.abs(value)}</span>
        </div>
      ),
    },
    {
      key: 'location',
      header: 'Location',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'operator_id',
      header: 'Operator',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'unit_cost',
      header: 'Unit Cost',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{value ? `$${value.toFixed(2)}` : '—'}</span>
      ),
    },
    {
      key: 'timestamp',
      header: 'Timestamp',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{formatDateTime(value)}</span>
      ),
    },
  ]



  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Inventory Dashboard</h1>
          <p className="text-gray-600">Monitor inventory transactions and movements</p>
        </div>
        <Button asChild>
          <Link to="/inventory/create">
            <Plus className="h-4 w-4 mr-2" />
            New Transaction
          </Link>
        </Button>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Transactions"
          value={isLoading ? "..." : totalTransactions.toString()}
          icon={Archive}
          iconColor="text-blue-600"
          iconBgColor="bg-blue-100"
          change={!isLoading ? {
            value: receipts,
            label: "receipts",
            type: "neutral"
          } : undefined}
        />

        <StatsCard
          title="Receipts"
          value={isLoading ? "..." : receipts.toString()}
          icon={TrendingUp}
          iconColor="text-green-600"
          iconBgColor="bg-green-100"
          change={!isLoading ? {
            value: Math.round((receipts / (totalTransactions || 1)) * 100),
            label: "of total",
            type: "increase"
          } : undefined}
        />

        <StatsCard
          title="Picks"
          value={isLoading ? "..." : picks.toString()}
          icon={Package}
          iconColor="text-purple-600"
          iconBgColor="bg-purple-100"
          change={!isLoading ? {
            value: Math.round((picks / (totalTransactions || 1)) * 100),
            label: "of total",
            type: "neutral"
          } : undefined}
        />

        <StatsCard
          title="Adjustments"
          value={isLoading ? "..." : adjustments.toString()}
          icon={TrendingDown}
          iconColor="text-yellow-600"
          iconBgColor="bg-yellow-100"
          change={!isLoading ? {
            value: Math.round((adjustments / (totalTransactions || 1)) * 100),
            label: "of total",
            type: adjustments > 0 ? "decrease" : "neutral"
          } : undefined}
        />
      </div>

      {/* Recent transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <DataState
            loading={isLoading}
            error={error}
            data={transactions}
            emptyMessage="No inventory transactions found"
          >
            <DataTable
              data={filteredTransactions}
              columns={columns}
              loading={isLoading}
              searchable
              searchPlaceholder="Search transactions..."
              onSearch={setSearchTerm}
              pagination={{
                page,
                size: pageSize,
                total: totalTransactions,
                onPageChange: setPage,
                onSizeChange: setPageSize,
              }}
            />
          </DataState>
        </CardContent>
      </Card>
    </div>
  )
}
