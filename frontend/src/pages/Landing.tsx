/**
 * Landing page for unauthenticated users.
 * 
 * This page serves as the entry point for users who are not logged in,
 * providing options to sign in or sign up for the warehouse management system.
 */

import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { 
  Warehouse, 
  BarChart3, 
  Users, 
  Truck, 
  Package, 
  Shield,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

export default function Landing() {
  const features = [
    {
      icon: <Package className="h-6 w-6" />,
      title: "Inventory Management",
      description: "Track and manage warehouse inventory with real-time updates"
    },
    {
      icon: <Truck className="h-6 w-6" />,
      title: "Vehicle Tracking",
      description: "Monitor vehicle movements and optimize logistics operations"
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: "Operator Management",
      description: "Track operator activities and performance metrics"
    },
    {
      icon: <BarChart3 className="h-6 w-6" />,
      title: "Analytics & Reports",
      description: "Comprehensive analytics and reporting dashboard"
    },
    {
      icon: <Shield className="h-6 w-6" />,
      title: "Secure Access",
      description: "Role-based access control and secure authentication"
    },
    {
      icon: <Warehouse className="h-6 w-6" />,
      title: "Warehouse Operations",
      description: "Streamline warehouse operations and workflows"
    }
  ];

  const benefits = [
    "Real-time inventory tracking",
    "Automated reporting and analytics",
    "Streamlined warehouse operations",
    "Enhanced security and access control",
    "Mobile-friendly responsive design",
    "Integration with existing systems"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Warehouse className="h-8 w-8 text-blue-600" />
              <span className="text-lg sm:text-xl font-bold text-gray-900">
                <span className="hidden sm:inline">Warehouse Management System</span>
                <span className="sm:hidden">WMS</span>
              </span>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-4">
              <Link to="/signin">
                <Button variant="ghost" size="sm" className="sm:size-default">
                  <span className="hidden sm:inline">Sign In</span>
                  <span className="sm:hidden">Login</span>
                </Button>
              </Link>
              <Link to="/signup">
                <Button size="sm" className="sm:size-default">
                  <span className="hidden sm:inline">Get Started</span>
                  <span className="sm:hidden">Join</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <Badge variant="secondary" className="mb-4">
            Modern Warehouse Management
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Streamline Your
            <span className="text-blue-600"> Warehouse Operations</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Comprehensive warehouse management system with real-time tracking, 
            analytics, and automated workflows to optimize your operations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/signup">
              <Button size="lg" className="w-full sm:w-auto">
                Start Free Trial
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            <Link to="/signin">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                Sign In to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Everything You Need to Manage Your Warehouse
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our comprehensive platform provides all the tools you need to optimize 
              warehouse operations and improve efficiency.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
                      {feature.icon}
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Why Choose Our Platform?
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Built with modern technology and designed for scalability, 
                our platform helps businesses of all sizes optimize their warehouse operations.
              </p>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="lg:pl-8">
              <Card className="border-0 shadow-2xl">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">Ready to Get Started?</CardTitle>
                  <CardDescription>
                    Join thousands of businesses already using our platform
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Link to="/signup" className="block">
                    <Button className="w-full" size="lg">
                      Create Free Account
                    </Button>
                  </Link>
                  <Link to="/signin" className="block">
                    <Button variant="outline" className="w-full" size="lg">
                      Sign In
                    </Button>
                  </Link>
                  <p className="text-sm text-gray-500 text-center">
                    No credit card required • Free 30-day trial
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center space-x-2 mb-8">
            <Warehouse className="h-6 w-6" />
            <span className="text-lg font-semibold">Warehouse Management System</span>
          </div>
          <div className="text-center text-gray-400">
            <p>&copy; 2024 Warehouse Management System. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
