/**
 * Integration tests for Dashboard page
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '../../test/utils'
import Dashboard from '../Dashboard'
import { mockFetch } from '../../test/setup'

describe('Dashboard Page', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders dashboard with all sections', async () => {
    render(<Dashboard />)
    
    // Check page header
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Overview of warehouse operations')).toBeInTheDocument()
    
    // Check stats cards
    expect(screen.getByText('Total Picks')).toBeInTheDocument()
    expect(screen.getByText('Active Operators')).toBeInTheDocument()
    expect(screen.getByText('Vehicles in Use')).toBeInTheDocument()
    expect(screen.getByText('Efficiency')).toBeInTheDocument()
    
    // Check stats values
    expect(screen.getByText('1,234')).toBeInTheDocument()
    expect(screen.getAllByText('45')[0]).toBeInTheDocument() // Use getAllByText for duplicate values
    expect(screen.getByText('28')).toBeInTheDocument()
    expect(screen.getByText('94.2%')).toBeInTheDocument()
  })

  it('displays recent picks section', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Recent Picks')).toBeInTheDocument()
    
    // Check for mock pick entries
    expect(screen.getByText('PICK-20240314-ABC123')).toBeInTheDocument()
    expect(screen.getByText('PICK-20240314-ABC223')).toBeInTheDocument()
    expect(screen.getByText('PICK-20240314-ABC323')).toBeInTheDocument()
    
    // Check for operator and location info
    expect(screen.getByText('Operator: OP-1001 • Location: A-01-01')).toBeInTheDocument()
    expect(screen.getByText('Operator: OP-2001 • Location: A-01-02')).toBeInTheDocument()
  })

  it('displays system alerts section', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('System Alerts')).toBeInTheDocument()
    
    // Check for alert items
    expect(screen.getByText('3 Failed Messages')).toBeInTheDocument()
    expect(screen.getByText('Queue processing errors detected')).toBeInTheDocument()
    
    expect(screen.getByText('Low Battery Alert')).toBeInTheDocument()
    expect(screen.getByText('Vehicle VH-001 battery at 15%')).toBeInTheDocument()
    
    expect(screen.getByText('Maintenance Scheduled')).toBeInTheDocument()
    expect(screen.getByText('Forklift FL-003 due for service')).toBeInTheDocument()
  })

  it('displays charts section', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Weekly Pick Volume')).toBeInTheDocument()
    expect(screen.getByText('Department Activity')).toBeInTheDocument()
    
    // Check for chart data labels
    expect(screen.getByText('Mon')).toBeInTheDocument()
    expect(screen.getByText('Tue')).toBeInTheDocument()
    expect(screen.getByText('Wed')).toBeInTheDocument()
    
    expect(screen.getByText('Picking')).toBeInTheDocument()
    expect(screen.getByText('Packing')).toBeInTheDocument()
    expect(screen.getByText('Shipping')).toBeInTheDocument()
    expect(screen.getByText('Receiving')).toBeInTheDocument()
  })

  it('shows correct change indicators in stats cards', () => {
    render(<Dashboard />)
    
    // Check for change indicators
    expect(screen.getByText('from last week')).toBeInTheDocument()
    expect(screen.getByText('from yesterday')).toBeInTheDocument()
    expect(screen.getByText('utilization')).toBeInTheDocument()
    expect(screen.getByText('from last month')).toBeInTheDocument()
  })

  it('has proper accessibility attributes', () => {
    render(<Dashboard />)
    
    // Check for proper heading hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 })
    expect(mainHeading).toHaveTextContent('Dashboard')
    
    // Check for section headings
    const sectionHeadings = screen.getAllByRole('heading', { level: 3 })
    expect(sectionHeadings.length).toBeGreaterThan(0)
  })

  it('renders responsive layout classes', () => {
    render(<Dashboard />)
    
    // Check for responsive grid classes
    const statsGrid = screen.getByText('Total Picks').closest('.grid')
    expect(statsGrid).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-4')
    
    const chartsGrid = screen.getByText('Weekly Pick Volume').closest('.grid')
    expect(chartsGrid).toHaveClass('grid-cols-1', 'lg:grid-cols-2')
  })

  it('displays all status badges correctly', () => {
    render(<Dashboard />)
    
    // Check for completed status badges
    const completedBadges = screen.getAllByText('Completed')
    expect(completedBadges.length).toBe(5) // 5 recent picks shown
    
    completedBadges.forEach(badge => {
      expect(badge).toHaveClass('bg-green-100', 'text-green-800')
    })
  })
})
