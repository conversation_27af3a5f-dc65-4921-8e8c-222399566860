/**
 * Failed Messages system page
 */

import { useState } from 'react'
import DataTable, { Column } from '../../components/ui/DataTable'
import Badge from '../../components/ui/Badge'
import Button from '../../components/ui/Button'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card'
import ErrorAlert from '../../components/ui/ErrorAlert'
import { RefreshCw, AlertTriangle, CheckCircle, X } from 'lucide-react'
import { FailedMessageResponse } from '../../lib/types'
import { formatDateTime } from '../../lib/utils'
import { useRetryFailedMessage, useResolveFailedMessage, useFailedMessages } from '../../hooks/use-system'

export default function FailedMessages() {
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // Fetch failed messages data from API
  const {
    data: failedMessagesData,
    isLoading,
    error,
    refetch
  } = useFailedMessages({
    page,
    size: pageSize,
    search: searchTerm || undefined
  })

  const retryMessage = useRetryFailedMessage()
  const resolveMessage = useResolveFailedMessage()

  const handleRetry = async (messageId: string) => {
    try {
      await retryMessage.mutateAsync(messageId)
    } catch (error) {
      console.error('Failed to retry message:', error)
    }
  }

  const handleResolve = async (messageId: string) => {
    try {
      await resolveMessage.mutateAsync(messageId)
    } catch (error) {
      console.error('Failed to resolve message:', error)
    }
  }

  const columns: Column<FailedMessageResponse>[] = [
    {
      key: 'message_id',
      header: 'Message ID',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'queue_name',
      header: 'Queue',
      sortable: true,
      render: (value) => (
        <Badge variant="outline">
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'event_type',
      header: 'Event Type',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{value?.replace('_', ' ') || '—'}</span>
      ),
    },
    {
      key: 'error_message',
      header: 'Error',
      render: (value) => (
        <div className="max-w-xs">
          <p className="text-sm text-red-600 truncate" title={value || ''}>
            {value || 'Unknown error'}
          </p>
        </div>
      ),
    },
    {
      key: 'retry_count',
      header: 'Retries',
      sortable: true,
      render: (value) => (
        <div className="flex items-center space-x-1">
          <RefreshCw className="h-3 w-3 text-gray-400" />
          <span className="text-sm">{value}</span>
        </div>
      ),
    },
    {
      key: 'resolved_at',
      header: 'Status',
      sortable: true,
      render: (value) => (
        <Badge variant={value ? 'success' : 'destructive'}>
          {value ? 'RESOLVED' : 'FAILED'}
        </Badge>
      ),
    },
    {
      key: 'first_failed_at',
      header: 'First Failed',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{formatDateTime(value)}</span>
      ),
    },
    {
      key: 'id',
      header: 'Actions',
      render: (value, row) => (
        <div className="flex items-center space-x-1">
          {!row.resolved_at && (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleRetry(String(row.id))}
                disabled={retryMessage.isPending}
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleResolve(String(row.id))}
                disabled={resolveMessage.isPending}
              >
                <CheckCircle className="h-3 w-3" />
              </Button>
            </>
          )}
        </div>
      ),
    },
  ]

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Failed Messages</h1>
            <p className="text-gray-600">Monitor and manage failed message processing</p>
          </div>
        </div>
        <ErrorAlert
          error={error}
          onRetry={refetch}
          title="Failed to load failed messages data"
        />
      </div>
    )
  }

  const messages = failedMessagesData?.items || []
  const unresolvedCount = messages.filter(m => !m.resolved_at).length
  const totalRetries = messages.reduce((acc, m) => acc + m.retry_count, 0)
  const avgRetries = messages.length > 0 ? totalRetries / messages.length : 0

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Failed Messages</h1>
          <p className="text-gray-600">Monitor and manage failed message processing</p>
        </div>
        <Button variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Quick stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <span className="text-sm font-medium text-gray-600">Failed Messages</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {isLoading ? "..." : unresolvedCount}
          </p>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="text-sm font-medium text-gray-600">Resolved</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {isLoading ? "..." : messages.length - unresolvedCount}
          </p>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium text-gray-600">Total Retries</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {isLoading ? "..." : totalRetries}
          </p>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span className="text-sm font-medium text-gray-600">Avg Retries</span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-1">
            {isLoading ? "..." : avgRetries.toFixed(1)}
          </p>
        </div>
      </div>

      {/* Failed messages table */}
      <Card>
        <CardHeader>
          <CardTitle>Failed Messages</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={messages}
            columns={columns}
            loading={isLoading}
            searchable
            searchPlaceholder="Search messages..."
            onSearch={setSearchTerm}
            pagination={{
              page,
              size: pageSize,
              total: failedMessagesData?.total || 0,
              onPageChange: setPage,
              onSizeChange: setPageSize,
            }}
          />
        </CardContent>
      </Card>
    </div>
  )
}
