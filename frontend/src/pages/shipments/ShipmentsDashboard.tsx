/**
 * Shipments Dashboard page
 */

import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import DataTable, { Column } from '../../components/ui/DataTable'
import Badge from '../../components/ui/Badge'
import Button from '../../components/ui/Button'
import StatsCard from '../../components/ui/StatsCard'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/Card'
import { Plus, Truck, Clock, CheckCircle, Package } from 'lucide-react'
import { ShipmentResponse, ShipmentStatus, ShipmentType, Priority } from '../../lib/types'
import { formatDateTime } from '../../lib/utils'

// Mock data for demonstration
const mockShipments: ShipmentResponse[] = [
  {
    id: 1,
    shipment_id: 'SHIP-20240314-001',
    customer_id: 'CUST-001',
    order_id: 'ORD-20240314-001',
    shipment_type: ShipmentType.STANDARD,
    shipment_status: ShipmentStatus.SHIPPED,
    carrier: 'UPS',
    tracking_number: '1Z999AA1234567890',
    priority: Priority.HIGH,
    total_weight_kg: 25.5,
    operator_id: 'OP-001',
    shipped_time: '2024-03-14T10:00:00Z',
    created_time: '2024-03-14T09:30:00Z',
    estimated_delivery: '2024-03-16T17:00:00Z',
    created_at: '2024-03-14T09:30:00Z',
    updated_at: '2024-03-14T10:00:00Z',
  },
  {
    id: 2,
    shipment_id: 'SHIP-20240314-002',
    customer_id: 'CUST-002',
    order_id: 'ORD-20240314-002',
    shipment_type: ShipmentType.EXPRESS,
    shipment_status: ShipmentStatus.PICKING,
    carrier: 'FedEx',
    tracking_number: '1234567890123456',
    priority: Priority.NORMAL,
    total_weight_kg: 15.2,
    operator_id: 'OP-002',
    created_time: '2024-03-14T11:00:00Z',
    estimated_delivery: '2024-03-17T12:00:00Z',
    created_at: '2024-03-14T11:00:00Z',
    updated_at: '2024-03-14T11:30:00Z',
  },
  {
    id: 3,
    shipment_id: 'SHIP-20240314-003',
    customer_id: 'CUST-003',
    order_id: 'ORD-20240314-003',
    shipment_type: ShipmentType.OVERNIGHT,
    shipment_status: ShipmentStatus.DELIVERED,
    carrier: 'DHL',
    tracking_number: 'DHL123456789',
    priority: Priority.LOW,
    total_weight_kg: 8.7,
    operator_id: 'OP-003',
    shipped_time: '2024-03-13T14:00:00Z',
    created_time: '2024-03-13T13:30:00Z',
    estimated_delivery: '2024-03-15T10:00:00Z',
    actual_delivery: '2024-03-15T09:45:00Z',
    created_at: '2024-03-13T13:30:00Z',
    updated_at: '2024-03-15T09:45:00Z',
  },
]

export default function ShipmentsDashboard() {
  const [searchTerm, setSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const columns: Column<ShipmentResponse>[] = [
    {
      key: 'shipment_id',
      header: 'Shipment ID',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      ),
    },
    {
      key: 'customer_id',
      header: 'Customer',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium text-sm">{(row as any).customer_name || value}</div>
          <div className="text-xs text-gray-500 font-mono">{value}</div>
        </div>
      ),
    },
    {
      key: 'carrier',
      header: 'Carrier',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium text-sm">{value}</div>
          <div className="text-xs text-gray-500 font-mono">{row.tracking_number}</div>
        </div>
      ),
    },
    {
      key: 'shipment_status',
      header: 'Status',
      sortable: true,
      render: (value) => (
        <Badge variant={
          value === ShipmentStatus.DELIVERED ? 'success' :
          value === ShipmentStatus.SHIPPED ? 'default' :
          value === ShipmentStatus.PICKING ? 'warning' :
          value === ShipmentStatus.CANCELLED ? 'destructive' : 'secondary'
        }>
          {value.replace('_', ' ').toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'priority',
      header: 'Priority',
      sortable: true,
      render: (value) => (
        <Badge variant={
          value === Priority.HIGH ? 'destructive' :
          value === Priority.NORMAL ? 'default' : 'secondary'
        }>
          {value.toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'total_weight_kg',
      header: 'Weight',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{value?.toFixed(1)} kg</span>
      ),
    },
    {
      key: 'estimated_delivery',
      header: 'Est. Delivery',
      sortable: true,
      render: (value, row) => (
        <div className="text-sm">
          {row.actual_delivery ? (
            <div>
              <div className="text-green-600">Delivered</div>
              <div className="text-xs text-gray-500">{formatDateTime(row.actual_delivery)}</div>
            </div>
          ) : (
            <div>{formatDateTime(value)}</div>
          )}
        </div>
      ),
    },
  ]

  const filteredData = mockShipments.filter(shipment =>
    shipment.shipment_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ((shipment as any).customer_name || shipment.customer_id).toLowerCase().includes(searchTerm.toLowerCase()) ||
    (shipment.carrier || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (shipment.tracking_number || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalShipments = mockShipments.length
  const preparing = mockShipments.filter(s => s.shipment_status === ShipmentStatus.PICKING).length
  const dispatched = mockShipments.filter(s => s.shipment_status === ShipmentStatus.SHIPPED).length
  const delivered = mockShipments.filter(s => s.shipment_status === ShipmentStatus.DELIVERED).length

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Shipments Dashboard</h1>
          <p className="text-gray-600">Monitor shipment status and delivery tracking</p>
        </div>
        <Button asChild>
          <Link to="/shipments/create">
            <Plus className="h-4 w-4 mr-2" />
            Create Shipment
          </Link>
        </Button>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Shipments"
          value={totalShipments.toString()}
          icon={Package}
          iconColor="text-blue-600"
          iconBgColor="bg-blue-100"
          change={{
            value: 18,
            label: "from last week",
            type: "increase"
          }}
        />
        
        <StatsCard
          title="Preparing"
          value={preparing.toString()}
          icon={Clock}
          iconColor="text-yellow-600"
          iconBgColor="bg-yellow-100"
          change={{
            value: 3,
            label: "from yesterday",
            type: "increase"
          }}
        />
        
        <StatsCard
          title="Dispatched"
          value={dispatched.toString()}
          icon={Truck}
          iconColor="text-purple-600"
          iconBgColor="bg-purple-100"
          change={{
            value: 5,
            label: "from yesterday",
            type: "increase"
          }}
        />
        
        <StatsCard
          title="Delivered"
          value={delivered.toString()}
          icon={CheckCircle}
          iconColor="text-green-600"
          iconBgColor="bg-green-100"
          change={{
            value: 12,
            label: "from yesterday",
            type: "increase"
          }}
        />
      </div>

      {/* Shipments table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Shipments</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={filteredData}
            columns={columns}
            searchable
            searchPlaceholder="Search shipments..."
            onSearch={setSearchTerm}
            pagination={{
              page,
              size: pageSize,
              total: filteredData.length,
              onPageChange: setPage,
              onSizeChange: setPageSize,
            }}
          />
        </CardContent>
      </Card>
    </div>
  )
}
