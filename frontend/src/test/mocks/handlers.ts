/**
 * Mock API handlers for testing
 */

import { http, HttpResponse } from 'msw'
import { 
  mockPick, 
  mockOperator, 
  mockVehicleMovement, 
  mockInventoryTransaction, 
  mockShipment, 
  mockFailedMessage,
  createPaginatedResponse 
} from '../utils'

const API_BASE_URL = 'http://localhost:8000/api/v1'

export const handlers = [
  // Picks endpoints
  http.get(`${API_BASE_URL}/picks/`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const size = parseInt(url.searchParams.get('size') || '50')
    
    const picks = Array.from({ length: 25 }, (_, i) => ({
      ...mockPick,
      id: i + 1,
      pick_id: `PICK-20240314-${String(i + 1).padStart(3, '0')}`,
    }))
    
    return HttpResponse.json(createPaginatedResponse(picks, page, size))
  }),

  http.get(`${API_BASE_URL}/picks/:pickId`, ({ params }) => {
    return HttpResponse.json({
      ...mockPick,
      pick_id: params.pickId,
    })
  }),

  http.post(`${API_BASE_URL}/picks/`, async ({ request }) => {
    const body = await request.json()
    return HttpResponse.json({
      ...mockPick,
      ...body,
      id: Math.floor(Math.random() * 1000),
    }, { status: 201 })
  }),

  http.put(`${API_BASE_URL}/picks/:pickId`, async ({ params, request }) => {
    const body = await request.json()
    return HttpResponse.json({
      ...mockPick,
      pick_id: params.pickId,
      ...body,
    })
  }),

  http.delete(`${API_BASE_URL}/picks/:pickId`, () => {
    return HttpResponse.json({ success: true })
  }),

  http.get(`${API_BASE_URL}/picks/statistics`, () => {
    return HttpResponse.json({
      total_picks: 1234,
      completed_picks: 1156,
      pending_picks: 78,
      completion_rate: 93.7,
      average_pick_time: 12.5,
    })
  }),

  // Operators endpoints
  http.get(`${API_BASE_URL}/operator-events/`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const size = parseInt(url.searchParams.get('size') || '50')
    
    const operators = Array.from({ length: 15 }, (_, i) => ({
      ...mockOperator,
      operator_id: `OP-${String(i + 1).padStart(3, '0')}`,
      employee_id: `EMP-${String(i + 1).padStart(8, '0')}`,
      full_name: `Operator ${i + 1}`,
    }))
    
    return HttpResponse.json(createPaginatedResponse(operators, page, size))
  }),

  // Vehicle movements endpoints
  http.get(`${API_BASE_URL}/vehicle-movements/`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const size = parseInt(url.searchParams.get('size') || '50')
    
    const movements = Array.from({ length: 30 }, (_, i) => ({
      ...mockVehicleMovement,
      id: i + 1,
      movement_id: `MOV-20240314-${String(i + 1).padStart(3, '0')}`,
      vehicle_id: `VH-${String((i % 10) + 1).padStart(3, '0')}`,
    }))
    
    return HttpResponse.json(createPaginatedResponse(movements, page, size))
  }),

  // Inventory transactions endpoints
  http.get(`${API_BASE_URL}/inventory-transactions/`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const size = parseInt(url.searchParams.get('size') || '50')
    
    const transactions = Array.from({ length: 20 }, (_, i) => ({
      ...mockInventoryTransaction,
      id: i + 1,
      transaction_id: `TXN-20240314-${String(i + 1).padStart(3, '0')}`,
    }))
    
    return HttpResponse.json(createPaginatedResponse(transactions, page, size))
  }),

  // Shipments endpoints
  http.get(`${API_BASE_URL}/shipments/`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const size = parseInt(url.searchParams.get('size') || '50')
    
    const shipments = Array.from({ length: 18 }, (_, i) => ({
      ...mockShipment,
      id: i + 1,
      shipment_id: `SHIP-20240314-${String(i + 1).padStart(3, '0')}`,
    }))
    
    return HttpResponse.json(createPaginatedResponse(shipments, page, size))
  }),

  // Failed messages endpoints
  http.get(`${API_BASE_URL}/failed-messages/`, ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const size = parseInt(url.searchParams.get('size') || '50')
    
    const messages = Array.from({ length: 8 }, (_, i) => ({
      ...mockFailedMessage,
      id: i + 1,
      message_id: `msg-${String(i + 1).padStart(3, '0')}`,
    }))
    
    return HttpResponse.json(createPaginatedResponse(messages, page, size))
  }),

  http.post(`${API_BASE_URL}/failed-messages/:messageId/retry`, ({ params }) => {
    return HttpResponse.json({ success: true, message_id: params.messageId })
  }),

  http.post(`${API_BASE_URL}/failed-messages/:messageId/resolve`, ({ params }) => {
    return HttpResponse.json({ success: true, message_id: params.messageId })
  }),

  // Health check
  http.get(`${API_BASE_URL}/health`, () => {
    return HttpResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
    })
  }),

  // Error handlers for testing
  http.get(`${API_BASE_URL}/picks/error-test`, () => {
    return HttpResponse.json(
      { detail: 'Test error message' },
      { status: 500 }
    )
  }),
]
