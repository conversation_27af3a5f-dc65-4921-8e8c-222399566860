/**
 * Test utilities for React components
 */

import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Mock data generators
export const mockPick = {
  id: 1,
  pick_id: 'PICK-20240314-ABC123',
  order_id: 'ORD-20240314-001',
  operator_id: 'OP-001',
  item_sku: 'SKU-WIDGET-001',
  item_description: 'Widget Assembly Kit',
  quantity_requested: 10,
  quantity_picked: 10,
  location_from: 'A-01-05',
  location_to: 'PACK-01',
  pick_status: 'completed' as const,
  pick_method: 'manual' as const,
  priority: 'normal' as const,
  start_time: '2024-03-14T10:30:00Z',
  end_time: '2024-03-14T10:45:00Z',
  notes: 'Test pick',
  created_at: '2024-03-14T10:25:00Z',
  updated_at: '2024-03-14T10:45:00Z',
}

export const mockOperator = {
  operator_id: 'OP-001',
  employee_id: 'EMP-12345678',
  full_name: 'John Smith',
  department: 'picking' as const,
  role: 'picker' as const,
  current_productivity: 95,
  current_safety_score: 98,
  total_tasks_completed: 1250,
  total_hours_worked: 160.5,
  average_performance: {
    picks_per_hour: 24.5,
    accuracy_rate: 98.2,
  },
}

export const mockVehicleMovement = {
  id: 1,
  movement_id: 'MOV-20240314-001',
  vehicle_id: 'VH-001',
  vehicle_type: 'forklift' as const,
  operator_id: 'OP-001',
  movement_type: 'pick' as const,
  start_location: 'A-01-05',
  end_location: 'PACK-01',
  start_time: '2024-03-14T10:30:00Z',
  end_time: '2024-03-14T10:45:00Z',
  distance_meters: 150,
  duration_seconds: 900,
  battery_level_start: 85,
  battery_level_end: 82,
  route_efficiency: 0.94,
  created_at: '2024-03-14T10:25:00Z',
  updated_at: '2024-03-14T10:45:00Z',
}

export const mockInventoryTransaction = {
  id: 1,
  transaction_id: 'TXN-20240314-001',
  item_sku: 'SKU-WIDGET-001',
  item_description: 'Widget Assembly Kit',
  transaction_type: 'receipt' as const,
  quantity: 100,
  unit_cost: 25.50,
  location: 'RECEIVING',
  operator_id: 'OP-001',
  reference_document: 'PO-20240314-001',
  timestamp: '2024-03-14T10:30:00Z',
  created_at: '2024-03-14T10:30:00Z',
  updated_at: '2024-03-14T10:30:00Z',
}

export const mockShipment = {
  id: 1,
  shipment_id: 'SHIP-20240314-001',
  customer_id: 'CUST-001',
  customer_name: 'Acme Corporation',
  carrier: 'UPS',
  tracking_number: '1Z999AA1234567890',
  status: 'dispatched' as const,
  priority: 'high' as const,
  total_weight: 25.5,
  total_value: 1250.00,
  ship_date: '2024-03-14T10:00:00Z',
  estimated_delivery: '2024-03-16T17:00:00Z',
  created_at: '2024-03-14T09:30:00Z',
  updated_at: '2024-03-14T10:00:00Z',
}

export const mockFailedMessage = {
  id: 1,
  message_id: 'msg-001',
  queue_name: 'pick_operations',
  event_type: 'pick_completed',
  message_body: {
    pick_id: 'PICK-20240314-ABC123',
    operator_id: 'OP-001',
    status: 'completed'
  },
  error_message: 'Database connection timeout',
  retry_count: 3,
  first_failed_at: '2024-03-14T10:30:00Z',
  last_retry_at: '2024-03-14T10:45:00Z',
  resolved_at: undefined,
  created_at: '2024-03-14T10:30:00Z',
}

// Helper to create paginated response
export const createPaginatedResponse = <T,>(items: T[], page = 1, size = 10) => ({
  items: items.slice((page - 1) * size, page * size),
  total: items.length,
  page,
  size,
  pages: Math.ceil(items.length / size),
})

// Helper to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
