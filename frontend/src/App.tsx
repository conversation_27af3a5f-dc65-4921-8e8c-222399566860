import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { BrowserRouter } from 'react-router-dom'
import { queryClient } from './lib/query-client'
import AppRoutes from './AppRoutes'
import ErrorBoundary from './components/ui/ErrorBoundary'
import { initializeAuth } from './stores/authStore'
import { useEffect } from 'react'

function App() {
  useEffect(() => {
    // Initialize authentication state on app start
    initializeAuth()
  }, [])

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <AppRoutes />
        </BrowserRouter>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </ErrorBoundary>
  )
}

export default App
