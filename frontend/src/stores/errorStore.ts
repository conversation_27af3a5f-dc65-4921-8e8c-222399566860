/**
 * Centralized error state management using Zustand
 */

import { create } from 'zustand'
import { ApiClientError, ApiErrorType } from '../lib/api'

export interface ApiStatus {
  isOnline: boolean
  lastChecked: Date
  consecutiveFailures: number
  lastError?: ApiClientError
}

interface ErrorState {
  // Global API status
  apiStatus: ApiStatus
  
  // Service-specific status
  serviceStatus: Record<string, ApiStatus>
  
  // Actions
  updateApiStatus: (status: Partial<ApiStatus>) => void
  updateServiceStatus: (service: string, status: Partial<ApiStatus>) => void
  recordError: (error: ApiClientError, service?: string) => void
  recordSuccess: (service?: string) => void
  isServiceAvailable: (service?: string) => boolean
  getServiceStatus: (service?: string) => ApiStatus
}

const initialApiStatus: ApiStatus = {
  isOnline: true,
  lastChecked: new Date(),
  consecutiveFailures: 0
}

export const useErrorStore = create<ErrorState>((set, get) => ({
  apiStatus: initialApiStatus,
  serviceStatus: {},

  updateApiStatus: (status) => set((state) => ({
    apiStatus: { ...state.apiStatus, ...status, lastChecked: new Date() }
  })),

  updateServiceStatus: (service, status) => set((state) => ({
    serviceStatus: {
      ...state.serviceStatus,
      [service]: {
        ...state.serviceStatus[service],
        ...status,
        lastChecked: new Date()
      }
    }
  })),

  recordError: (error, service) => {
    const isOfflineError = error.type === ApiErrorType.OFFLINE_ERROR || 
                          error.type === ApiErrorType.TIMEOUT_ERROR

    if (service) {
      // Update service-specific status
      set((state) => {
        const currentStatus = state.serviceStatus[service] || initialApiStatus
        return {
          serviceStatus: {
            ...state.serviceStatus,
            [service]: {
              isOnline: !isOfflineError,
              lastChecked: new Date(),
              consecutiveFailures: currentStatus.consecutiveFailures + 1,
              lastError: error
            }
          }
        }
      })
    } else {
      // Update global API status
      set((state) => ({
        apiStatus: {
          isOnline: !isOfflineError,
          lastChecked: new Date(),
          consecutiveFailures: state.apiStatus.consecutiveFailures + 1,
          lastError: error
        }
      }))
    }
  },

  recordSuccess: (service) => {
    if (service) {
      // Update service-specific status
      set((state) => ({
        serviceStatus: {
          ...state.serviceStatus,
          [service]: {
            isOnline: true,
            lastChecked: new Date(),
            consecutiveFailures: 0,
            lastError: undefined
          }
        }
      }))
    } else {
      // Update global API status
      set((state) => ({
        apiStatus: {
          isOnline: true,
          lastChecked: new Date(),
          consecutiveFailures: 0,
          lastError: undefined
        }
      }))
    }
  },

  isServiceAvailable: (service) => {
    const state = get()
    if (service) {
      const serviceStatus = state.serviceStatus[service]
      return serviceStatus ? serviceStatus.isOnline : state.apiStatus.isOnline
    }
    return state.apiStatus.isOnline
  },

  getServiceStatus: (service) => {
    const state = get()
    if (service) {
      return state.serviceStatus[service] || state.apiStatus
    }
    return state.apiStatus
  }
}))

/**
 * Hook to track API calls and update error state
 */
export function useApiErrorTracking() {
  const { recordError, recordSuccess } = useErrorStore()

  const trackApiCall = async <T>(
    apiCall: () => Promise<T>,
    service?: string
  ): Promise<T> => {
    try {
      const result = await apiCall()
      recordSuccess(service)
      return result
    } catch (error) {
      if (error instanceof ApiClientError) {
        recordError(error, service)
      }
      throw error
    }
  }

  return { trackApiCall }
}
