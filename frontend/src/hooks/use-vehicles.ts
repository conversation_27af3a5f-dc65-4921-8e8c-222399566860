/**
 * Custom hooks for Vehicle Movements API operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '../lib/api'
import { queryKeys } from '../lib/query-client'
import {
  VehicleMovementCreate,
  VehicleMovementUpdate,
  PaginationParams,
} from '../lib/types'

// Query hooks
export function useVehicleMovements(params?: PaginationParams) {
  return useQuery({
    queryKey: queryKeys.vehicles.movementsList(params),
    queryFn: () => apiClient.vehicles.listMovements(params),
  })
}

export function useVehicleMovement(movementId: string) {
  return useQuery({
    queryKey: queryKeys.vehicles.movementDetail(movementId),
    queryFn: () => apiClient.vehicles.getMovement(movementId),
    enabled: !!movementId,
  })
}

export function useVehicleEfficiency(vehicleId: string) {
  return useQuery({
    queryKey: queryKeys.vehicles.efficiency(vehicleId),
    queryFn: () => apiClient.vehicles.getEfficiencyMetrics(vehicleId),
    enabled: !!vehicleId,
  })
}

// Mutation hooks
export function useCreateVehicleMovement() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: VehicleMovementCreate) => apiClient.vehicles.createMovement(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles.movements() })
    },
  })
}

export function useUpdateVehicleMovement() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ movementId, data }: { movementId: string; data: VehicleMovementUpdate }) =>
      apiClient.vehicles.updateMovement(movementId, data),
    onSuccess: (updatedMovement) => {
      queryClient.setQueryData(
        queryKeys.vehicles.movementDetail(updatedMovement.movement_id),
        updatedMovement
      )
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles.movements() })
    },
  })
}

export function useDeleteVehicleMovement() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (movementId: string) => apiClient.vehicles.deleteMovement(movementId),
    onSuccess: (_, movementId) => {
      queryClient.removeQueries({ queryKey: queryKeys.vehicles.movementDetail(movementId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles.movements() })
    },
  })
}
