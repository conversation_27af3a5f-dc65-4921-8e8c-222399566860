/**
 * Custom hooks for Picks API operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '../lib/api'
import { queryKeys } from '../lib/query-client'
import {
  PickC<PERSON>,
  PickUpdate,
  PickFilterParams,
  PaginationParams,
} from '../lib/types'

// Query hooks
export function usePicks(params?: PickFilterParams & PaginationParams) {
  return useQuery({
    queryKey: queryKeys.picks.list(params),
    queryFn: () => apiClient.picks.list(params),
  })
}

export function usePick(pickId: string) {
  return useQuery({
    queryKey: queryKeys.picks.detail(pickId),
    queryFn: () => apiClient.picks.get(pickId),
    enabled: !!pickId,
  })
}

export function usePickStatistics() {
  return useQuery({
    queryKey: queryKeys.picks.statistics(),
    queryFn: () => apiClient.picks.getStatistics(),
  })
}

export function usePicksByOperator(operatorId: string) {
  return useQuery({
    queryKey: queryKeys.picks.byOperator(operatorId),
    queryFn: () => apiClient.picks.getByOperator(operatorId),
    enabled: !!operatorId,
  })
}

export function usePicksByBatch(batchId: string) {
  return useQuery({
    queryKey: queryKeys.picks.byBatch(batchId),
    queryFn: () => apiClient.picks.getByBatch(batchId),
    enabled: !!batchId,
  })
}

// Mutation hooks
export function useCreatePick() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: PickCreate) => apiClient.picks.create(data),
    onSuccess: () => {
      // Invalidate and refetch picks list
      queryClient.invalidateQueries({ queryKey: queryKeys.picks.lists() })
      queryClient.invalidateQueries({ queryKey: queryKeys.picks.statistics() })
    },
  })
}

export function useUpdatePick() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ pickId, data }: { pickId: string; data: PickUpdate }) =>
      apiClient.picks.update(pickId, data),
    onSuccess: (updatedPick) => {
      // Update the specific pick in cache
      queryClient.setQueryData(
        queryKeys.picks.detail(updatedPick.pick_id),
        updatedPick
      )
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: queryKeys.picks.lists() })
      queryClient.invalidateQueries({ queryKey: queryKeys.picks.statistics() })
    },
  })
}

export function useDeletePick() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (pickId: string) => apiClient.picks.delete(pickId),
    onSuccess: (_, pickId) => {
      // Remove the pick from cache
      queryClient.removeQueries({ queryKey: queryKeys.picks.detail(pickId) })
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: queryKeys.picks.lists() })
      queryClient.invalidateQueries({ queryKey: queryKeys.picks.statistics() })
    },
  })
}
