/**
 * Keyboard shortcuts hook for enhanced accessibility and power user features
 */

import { useEffect, useCallback } from 'react'

interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  action: () => void
  description: string
  preventDefault?: boolean
}

interface UseKeyboardShortcutsOptions {
  shortcuts: KeyboardShortcut[]
  enabled?: boolean
  scope?: 'global' | 'local'
  element?: HTMLElement | null
}

export function useKeyboardShortcuts({
  shortcuts,
  enabled = true,
  scope = 'global',
  element
}: UseKeyboardShortcutsOptions) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return

    // Don't trigger shortcuts when user is typing in form fields
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return
    }

    for (const shortcut of shortcuts) {
      const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase()
      const ctrlMatches = !!shortcut.ctrlKey === event.ctrlKey
      const altMatches = !!shortcut.altKey === event.altKey
      const shiftMatches = !!shortcut.shiftKey === event.shiftKey
      const metaMatches = !!shortcut.metaKey === event.metaKey

      if (keyMatches && ctrlMatches && altMatches && shiftMatches && metaMatches) {
        if (shortcut.preventDefault !== false) {
          event.preventDefault()
        }
        shortcut.action()
        break
      }
    }
  }, [shortcuts, enabled])

  useEffect(() => {
    const targetElement = scope === 'local' && element ? element : document

    targetElement.addEventListener('keydown', handleKeyDown)
    return () => targetElement.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown, scope, element])

  return {
    shortcuts: shortcuts.map(s => ({
      key: s.key,
      modifiers: [
        s.ctrlKey && 'Ctrl',
        s.altKey && 'Alt',
        s.shiftKey && 'Shift',
        s.metaKey && 'Cmd'
      ].filter(Boolean).join(' + '),
      description: s.description
    }))
  }
}

// Common keyboard shortcuts
export const commonShortcuts = {
  search: {
    key: 'k',
    ctrlKey: true,
    description: 'Open search'
  },
  help: {
    key: '?',
    shiftKey: true,
    description: 'Show help'
  },
  escape: {
    key: 'Escape',
    description: 'Close modal/cancel'
  },
  save: {
    key: 's',
    ctrlKey: true,
    description: 'Save'
  },
  refresh: {
    key: 'r',
    ctrlKey: true,
    description: 'Refresh'
  },
  newItem: {
    key: 'n',
    ctrlKey: true,
    description: 'Create new item'
  }
}

// Hook for global application shortcuts
export function useGlobalShortcuts() {
  const shortcuts: KeyboardShortcut[] = [
    {
      ...commonShortcuts.search,
      action: () => {
        // Focus search input if available
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i]') as HTMLInputElement
        if (searchInput) {
          searchInput.focus()
          searchInput.select()
        }
      }
    },
    {
      ...commonShortcuts.help,
      action: () => {
        // Show help modal or navigate to help page
        console.log('Help shortcut triggered')
        // This could open a help modal or navigate to help
      }
    }
  ]

  return useKeyboardShortcuts({ shortcuts })
}

// Hook for data table shortcuts
export function useDataTableShortcuts(actions: {
  onRefresh?: () => void
  onNew?: () => void
  onSearch?: () => void
}) {
  const shortcuts: KeyboardShortcut[] = [
    ...(actions.onRefresh ? [{
      key: 'r',
      ctrlKey: true,
      action: actions.onRefresh,
      description: 'Refresh table data'
    }] : []),
    ...(actions.onNew ? [{
      key: 'n',
      ctrlKey: true,
      action: actions.onNew,
      description: 'Create new item'
    }] : []),
    ...(actions.onSearch ? [{
      key: 'f',
      ctrlKey: true,
      action: actions.onSearch,
      description: 'Focus search'
    }] : [])
  ]

  return useKeyboardShortcuts({ shortcuts })
}

// Hook for form shortcuts
export function useFormShortcuts(actions: {
  onSave?: () => void
  onCancel?: () => void
  onReset?: () => void
}) {
  const shortcuts: KeyboardShortcut[] = [
    ...(actions.onSave ? [{
      key: 's',
      ctrlKey: true,
      action: actions.onSave,
      description: 'Save form'
    }] : []),
    ...(actions.onCancel ? [{
      key: 'Escape',
      action: actions.onCancel,
      description: 'Cancel form'
    }] : []),
    ...(actions.onReset ? [{
      key: 'r',
      ctrlKey: true,
      shiftKey: true,
      action: actions.onReset,
      description: 'Reset form'
    }] : [])
  ]

  return useKeyboardShortcuts({ shortcuts })
}

// Screen reader announcements
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', priority)
  announcement.setAttribute('aria-atomic', 'true')
  announcement.className = 'sr-only'
  announcement.textContent = message
  
  document.body.appendChild(announcement)
  
  // Remove after announcement
  setTimeout(() => {
    if (document.body.contains(announcement)) {
      document.body.removeChild(announcement)
    }
  }, 1000)
}

export default useKeyboardShortcuts
