/**
 * Custom hooks for Operator Events API operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '../lib/api'
import { queryKeys } from '../lib/query-client'
import {
  OperatorEventCreate,
  OperatorEventUpdate,
  PaginationParams,
} from '../lib/types'

// Query hooks
export function useOperatorEvents(params?: PaginationParams) {
  return useQuery({
    queryKey: queryKeys.operators.eventsList(params),
    queryFn: () => apiClient.operators.listEvents(params),
  })
}

export function useOperatorEvent(eventId: string) {
  return useQuery({
    queryKey: queryKeys.operators.eventDetail(eventId),
    queryFn: () => apiClient.operators.getEvent(eventId),
    enabled: !!eventId,
  })
}

export function useOperatorSummary(operatorId: string) {
  return useQuery({
    queryKey: queryKeys.operators.summary(operatorId),
    queryFn: () => apiClient.operators.getSummary(operatorId),
    enabled: !!operatorId,
  })
}

export function useOperatorPerformance(operatorId: string) {
  return useQuery({
    queryKey: queryKeys.operators.performance(operatorId),
    queryFn: () => apiClient.operators.getPerformanceMetrics(operatorId),
    enabled: !!operatorId,
  })
}

export function useOperatorStatistics(params?: { start_date?: string; end_date?: string; department?: string }) {
  return useQuery({
    queryKey: queryKeys.operators.statistics(params),
    queryFn: () => apiClient.operators.getStatistics(params),
  })
}

// Mutation hooks
export function useCreateOperatorEvent() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: OperatorEventCreate) => apiClient.operators.createEvent(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.operators.events() })
    },
  })
}

export function useUpdateOperatorEvent() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ eventId, data }: { eventId: string; data: OperatorEventUpdate }) =>
      apiClient.operators.updateEvent(eventId, data),
    onSuccess: (updatedEvent) => {
      queryClient.setQueryData(
        queryKeys.operators.eventDetail(updatedEvent.event_id),
        updatedEvent
      )
      queryClient.invalidateQueries({ queryKey: queryKeys.operators.events() })
    },
  })
}

export function useDeleteOperatorEvent() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (eventId: string) => apiClient.operators.deleteEvent(eventId),
    onSuccess: (_, eventId) => {
      queryClient.removeQueries({ queryKey: queryKeys.operators.eventDetail(eventId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.operators.events() })
    },
  })
}
