/**
 * Hook for detecting network connectivity status
 */

import { useState, useEffect } from 'react'

export interface NetworkStatus {
  isOnline: boolean
  isConnecting: boolean
  lastOnline?: Date
  lastOffline?: Date
}

export function useNetworkStatus(): NetworkStatus {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    isConnecting: false,
  })

  useEffect(() => {
    const handleOnline = () => {
      setNetworkStatus(prev => ({
        ...prev,
        isOnline: true,
        isConnecting: false,
        lastOnline: new Date(),
      }))
    }

    const handleOffline = () => {
      setNetworkStatus(prev => ({
        ...prev,
        isOnline: false,
        isConnecting: false,
        lastOffline: new Date(),
      }))
    }

    // Listen for network status changes
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Check connection periodically when offline
    let intervalId: NodeJS.Timeout | null = null
    
    if (!networkStatus.isOnline) {
      intervalId = setInterval(() => {
        // Try to fetch a small resource to test connectivity
        fetch('/favicon.ico', { 
          method: 'HEAD',
          cache: 'no-cache',
          mode: 'no-cors'
        })
        .then(() => {
          if (!navigator.onLine) {
            // Browser thinks we're offline but we can reach resources
            handleOnline()
          }
        })
        .catch(() => {
          // Still offline
        })
      }, 5000) // Check every 5 seconds when offline
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [networkStatus.isOnline])

  return networkStatus
}

/**
 * Hook for testing backend connectivity specifically
 */
export function useBackendConnectivity(baseUrl?: string) {
  const [isBackendOnline, setIsBackendOnline] = useState<boolean | null>(null)
  const [lastChecked, setLastChecked] = useState<Date | null>(null)
  const [isChecking, setIsChecking] = useState(false)
  
  const networkStatus = useNetworkStatus()
  const apiBaseUrl = baseUrl || import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

  const checkBackendConnectivity = async (): Promise<boolean> => {
    if (!networkStatus.isOnline) {
      return false
    }

    setIsChecking(true)

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 second timeout

      // Health endpoint is at root level, not under /api/v1
      const baseUrl = apiBaseUrl.replace('/api/v1', '')
      const response = await fetch(`${baseUrl}/health`, {
        method: 'GET',
        signal: controller.signal,
        cache: 'no-cache',
      })
      
      clearTimeout(timeoutId)
      const isOnline = response.ok
      
      setIsBackendOnline(isOnline)
      setLastChecked(new Date())
      
      return isOnline
    } catch (error) {
      setIsBackendOnline(false)
      setLastChecked(new Date())
      return false
    } finally {
      setIsChecking(false)
    }
  }

  useEffect(() => {
    // Check backend connectivity when network comes online
    if (networkStatus.isOnline && isBackendOnline === null) {
      checkBackendConnectivity()
    }
  }, [networkStatus.isOnline])

  useEffect(() => {
    // Periodic health check when online
    if (networkStatus.isOnline) {
      const intervalId = setInterval(() => {
        checkBackendConnectivity()
      }, 30000) // Check every 30 seconds

      return () => clearInterval(intervalId)
    }
  }, [networkStatus.isOnline])

  return {
    isBackendOnline,
    isChecking,
    lastChecked,
    checkBackendConnectivity,
    networkStatus,
  }
}
