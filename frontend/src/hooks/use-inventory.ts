/**
 * Custom hooks for Inventory Transactions API operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '../lib/api'
import { queryKeys } from '../lib/query-client'
import {
  InventoryTransactionCreate,
  PaginationParams,
} from '../lib/types'

// Query hooks
export function useInventoryTransactions(params?: PaginationParams) {
  return useQuery({
    queryKey: queryKeys.inventory.transactionsList(params),
    queryFn: () => apiClient.inventory.listTransactions(params),
  })
}

export function useInventoryTransaction(transactionId: string) {
  return useQuery({
    queryKey: queryKeys.inventory.transactionDetail(transactionId),
    queryFn: () => apiClient.inventory.getTransaction(transactionId),
    enabled: !!transactionId,
  })
}

export function useInventoryByItem(itemSku: string) {
  return useQuery({
    queryKey: queryKeys.inventory.byItem(itemSku),
    queryFn: () => apiClient.inventory.getByItem(itemSku),
    enabled: !!itemSku,
  })
}

export function useInventoryByLocation(location: string) {
  return useQuery({
    queryKey: queryKeys.inventory.byLocation(location),
    queryFn: () => apiClient.inventory.getByLocation(location),
    enabled: !!location,
  })
}

export function useInventoryStatistics(params?: { start_date?: string; end_date?: string; location?: string }) {
  return useQuery({
    queryKey: queryKeys.inventory.statistics(params),
    queryFn: () => apiClient.inventory.getStatistics(params),
  })
}

// Mutation hooks
export function useCreateInventoryTransaction() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: InventoryTransactionCreate) => apiClient.inventory.createTransaction(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.inventory.transactions() })
    },
  })
}
