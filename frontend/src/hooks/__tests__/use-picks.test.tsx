/**
 * Tests for use-picks hooks
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { usePicks, useCreatePick, useUpdatePick, useDeletePick } from '../use-picks'
import { mockFetch } from '../../test/setup'
import { mockPick, createPaginatedResponse } from '../../test/utils'
import { PickStatus } from '../../lib/types'
import React from 'react'

// Create wrapper for React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false, gcTime: 0 },
      mutations: { retry: false },
    },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('use-picks hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('usePicks', () => {
    it('fetches picks successfully', async () => {
      const mockResponse = createPaginatedResponse([mockPick])
      mockFetch(mockResponse)

      const { result } = renderHook(() => usePicks(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockResponse)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/picks/',
        expect.any(Object)
      )
    })

    it('handles fetch error', async () => {
      mockFetch({ detail: 'Server error' }, false)

      const { result } = renderHook(() => usePicks(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeTruthy()
    })

    it('passes pagination parameters', async () => {
      const mockResponse = createPaginatedResponse([mockPick], 2, 20)
      mockFetch(mockResponse)

      const { result } = renderHook(
        () => usePicks({ page: 2, size: 20 }),
        { wrapper: createWrapper() }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/picks/?page=2&size=20',
        expect.any(Object)
      )
    })
  })

  describe('useCreatePick', () => {
    it('creates pick successfully', async () => {
      const newPick = {
        pick_id: 'PICK-20240314-NEW001',
        order_id: 'ORD-20240314-002',
        operator_id: 'OP-002',
        item_sku: 'SKU-GADGET-002',
        quantity_requested: 5,
        location_from: 'B-02-10',
        pick_status: PickStatus.PENDING,
        start_time: '2024-03-14T11:00:00Z',
        quantity_picked: 0,
      }

      const mockResponse = { ...mockPick, ...newPick }
      mockFetch(mockResponse)

      const { result } = renderHook(() => useCreatePick(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        result.current.mutate(newPick)
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockResponse)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/picks/',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(newPick),
        })
      )
    })

    it('handles create error', async () => {
      mockFetch({ detail: 'Validation error' }, false)

      const { result } = renderHook(() => useCreatePick(), {
        wrapper: createWrapper(),
      })

      const newPick = {
        pick_id: 'INVALID',
        order_id: '',
        operator_id: '',
        item_sku: '',
        quantity_requested: 0,
        location_from: '',
        pick_status: PickStatus.PENDING,
        start_time: '',
        quantity_picked: 0,
      }

      await waitFor(() => {
        result.current.mutate(newPick)
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeTruthy()
    })
  })

  describe('useUpdatePick', () => {
    it('updates pick successfully', async () => {
      const updateData = {
        pick_status: PickStatus.COMPLETED,
        quantity_picked: 10,
        end_time: '2024-03-14T11:15:00Z',
      }

      const mockResponse = { ...mockPick, ...updateData }
      mockFetch(mockResponse)

      const { result } = renderHook(() => useUpdatePick(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        result.current.mutate({
          pickId: 'PICK-20240314-ABC123',
          data: updateData,
        })
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockResponse)
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/picks/PICK-20240314-ABC123',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updateData),
        })
      )
    })
  })

  describe('useDeletePick', () => {
    it('deletes pick successfully', async () => {
      mockFetch({ success: true })

      const { result } = renderHook(() => useDeletePick(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        result.current.mutate('PICK-20240314-ABC123')
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/picks/PICK-20240314-ABC123',
        expect.objectContaining({
          method: 'DELETE',
        })
      )
    })
  })
})
