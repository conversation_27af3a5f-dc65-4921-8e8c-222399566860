/**
 * Custom hooks for Shipments API operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '../lib/api'
import { queryKeys } from '../lib/query-client'
import {
  ShipmentCreate,
  ShipmentUpdate,
  PaginationParams,
} from '../lib/types'

// Query hooks
export function useShipments(params?: PaginationParams) {
  return useQuery({
    queryKey: queryKeys.shipments.list(params),
    queryFn: () => apiClient.shipments.list(params),
  })
}

export function useShipment(shipmentId: string) {
  return useQuery({
    queryKey: queryKeys.shipments.detail(shipmentId),
    queryFn: () => apiClient.shipments.get(shipmentId),
    enabled: !!shipmentId,
  })
}

export function useShipmentsByCustomer(customerId: string) {
  return useQuery({
    queryKey: queryKeys.shipments.byCustomer(customerId),
    queryFn: () => apiClient.shipments.getByCustomer(customerId),
    enabled: !!customerId,
  })
}

// Mutation hooks
export function useCreateShipment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: ShipmentCreate) => apiClient.shipments.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.shipments.lists() })
    },
  })
}

export function useUpdateShipment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ shipmentId, data }: { shipmentId: string; data: ShipmentUpdate }) =>
      apiClient.shipments.update(shipmentId, data),
    onSuccess: (updatedShipment) => {
      queryClient.setQueryData(
        queryKeys.shipments.detail(updatedShipment.shipment_id),
        updatedShipment
      )
      queryClient.invalidateQueries({ queryKey: queryKeys.shipments.lists() })
    },
  })
}

export function useDeleteShipment() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (shipmentId: string) => apiClient.shipments.delete(shipmentId),
    onSuccess: (_, shipmentId) => {
      queryClient.removeQueries({ queryKey: queryKeys.shipments.detail(shipmentId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.shipments.lists() })
    },
  })
}
