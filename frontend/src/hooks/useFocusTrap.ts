/**
 * Focus trap hook for accessibility
 */

import { useEffect, useRef } from 'react'

interface UseFocusTrapOptions {
  isActive: boolean
  initialFocus?: HTMLElement | null
  restoreFocus?: boolean
}

export function useFocusTrap({ 
  isActive, 
  initialFocus, 
  restoreFocus = true 
}: UseFocusTrapOptions) {
  const containerRef = useRef<HTMLElement>(null)
  const previousActiveElement = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (!isActive) return

    // Store the currently focused element
    previousActiveElement.current = document.activeElement as HTMLElement

    const container = containerRef.current
    if (!container) return

    // Get all focusable elements within the container
    const getFocusableElements = (): HTMLElement[] => {
      const focusableSelectors = [
        'button:not([disabled])',
        'input:not([disabled])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        'a[href]',
        '[tabindex]:not([tabindex="-1"])',
        '[contenteditable="true"]'
      ].join(', ')

      return Array.from(container.querySelectorAll(focusableSelectors))
        .filter((element) => {
          const el = element as HTMLElement
          return el.offsetWidth > 0 && el.offsetHeight > 0 && !el.hidden
        }) as HTMLElement[]
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return

      const focusableElements = getFocusableElements()
      if (focusableElements.length === 0) return

      const firstElement = focusableElements[0]
      const lastElement = focusableElements[focusableElements.length - 1]

      if (event.shiftKey) {
        // Shift + Tab: move to previous element
        if (document.activeElement === firstElement) {
          event.preventDefault()
          lastElement.focus()
        }
      } else {
        // Tab: move to next element
        if (document.activeElement === lastElement) {
          event.preventDefault()
          firstElement.focus()
        }
      }
    }

    // Set initial focus
    const focusableElements = getFocusableElements()
    if (initialFocus && focusableElements.includes(initialFocus)) {
      initialFocus.focus()
    } else if (focusableElements.length > 0) {
      focusableElements[0].focus()
    }

    // Add event listener
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      
      // Restore focus to the previously focused element
      if (restoreFocus && previousActiveElement.current) {
        previousActiveElement.current.focus()
      }
    }
  }, [isActive, initialFocus, restoreFocus])

  return containerRef
}

// Hook for managing focus within a specific element
export function useFocusWithin() {
  const elementRef = useRef<HTMLElement>(null)

  const focusFirst = () => {
    const element = elementRef.current
    if (!element) return

    const focusableElement = element.querySelector(
      'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
    ) as HTMLElement

    if (focusableElement) {
      focusableElement.focus()
    }
  }

  const focusLast = () => {
    const element = elementRef.current
    if (!element) return

    const focusableElements = element.querySelectorAll(
      'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
    )

    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
    if (lastElement) {
      lastElement.focus()
    }
  }

  return {
    elementRef,
    focusFirst,
    focusLast
  }
}

// Hook for keyboard navigation
export function useKeyboardNavigation(
  items: any[],
  onSelect: (item: any, index: number) => void,
  isActive: boolean = true
) {
  const selectedIndexRef = useRef(0)

  useEffect(() => {
    if (!isActive || items.length === 0) return

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          selectedIndexRef.current = Math.min(selectedIndexRef.current + 1, items.length - 1)
          break
        case 'ArrowUp':
          event.preventDefault()
          selectedIndexRef.current = Math.max(selectedIndexRef.current - 1, 0)
          break
        case 'Home':
          event.preventDefault()
          selectedIndexRef.current = 0
          break
        case 'End':
          event.preventDefault()
          selectedIndexRef.current = items.length - 1
          break
        case 'Enter':
        case ' ':
          event.preventDefault()
          onSelect(items[selectedIndexRef.current], selectedIndexRef.current)
          break
        case 'Escape':
          event.preventDefault()
          // Let parent handle escape
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [items, onSelect, isActive])

  return {
    selectedIndex: selectedIndexRef.current,
    setSelectedIndex: (index: number) => {
      selectedIndexRef.current = Math.max(0, Math.min(index, items.length - 1))
    }
  }
}

export default useFocusTrap
