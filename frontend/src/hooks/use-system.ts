/**
 * Custom hooks for System/Failed Messages API operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '../lib/api'
import { queryKeys } from '../lib/query-client'
import {
  FailedMessageCreate,
  FailedMessageUpdate,
  PaginationParams,
} from '../lib/types'

// Query hooks
export function useFailedMessages(params?: PaginationParams) {
  return useQuery({
    queryKey: queryKeys.system.failedMessagesList(params),
    queryFn: () => apiClient.system.listFailedMessages(params),
  })
}

export function useFailedMessage(messageId: string) {
  return useQuery({
    queryKey: queryKeys.system.failedMessageDetail(messageId),
    queryFn: () => apiClient.system.getFailedMessage(messageId),
    enabled: !!messageId,
  })
}

export function useFailureAnalysis(messageId: string) {
  return useQuery({
    queryKey: queryKeys.system.failureAnalysis(messageId),
    queryFn: () => apiClient.system.getFailureAnalysis(messageId),
    enabled: !!messageId,
  })
}

// Mutation hooks
export function useCreateFailedMessage() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: FailedMessageCreate) => apiClient.system.createFailedMessage(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.system.failedMessages() })
    },
  })
}

export function useUpdateFailedMessage() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ messageId, data }: { messageId: string; data: FailedMessageUpdate }) =>
      apiClient.system.updateFailedMessage(messageId, data),
    onSuccess: (updatedMessage) => {
      queryClient.setQueryData(
        queryKeys.system.failedMessageDetail(String(updatedMessage.id)),
        updatedMessage
      )
      queryClient.invalidateQueries({ queryKey: queryKeys.system.failedMessages() })
    },
  })
}

export function useRetryFailedMessage() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (messageId: string) => apiClient.system.retryFailedMessage(messageId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.system.failedMessages() })
    },
  })
}

export function useResolveFailedMessage() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (messageId: string) => apiClient.system.resolveFailedMessage(messageId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.system.failedMessages() })
    },
  })
}
