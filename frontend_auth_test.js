// Frontend Authentication Test Script
// Run this in the browser console on http://localhost:5174

async function testFrontendAuth() {
    console.log('🧪 Starting Frontend Authentication Tests...');
    
    try {
        // Test 1: Check if the frontend can reach the backend
        console.log('\n1️⃣ Testing backend connectivity...');
        const healthResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'admin123'
            })
        });
        
        if (healthResponse.ok) {
            const loginData = await healthResponse.json();
            console.log('✅ Backend connectivity: OK');
            console.log('✅ Login endpoint: Working');
            console.log('Token received:', loginData.access_token.substring(0, 20) + '...');
            
            // Test 2: Test the /me endpoint
            console.log('\n2️⃣ Testing user profile endpoint...');
            const userResponse = await fetch('http://localhost:8000/api/v1/auth/me', {
                headers: {
                    'Authorization': `Bearer ${loginData.access_token}`
                }
            });
            
            if (userResponse.ok) {
                const userData = await userResponse.json();
                console.log('✅ User profile endpoint: Working');
                console.log('User data:', userData);
            } else {
                console.log('❌ User profile endpoint failed:', userResponse.status);
            }
            
            // Test 3: Test signup
            console.log('\n3️⃣ Testing signup endpoint...');
            const signupResponse = await fetch('http://localhost:8000/api/v1/auth/signup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: `test${Date.now()}@example.com`,
                    password: 'testpass123',
                    full_name: 'Test User'
                })
            });
            
            if (signupResponse.ok) {
                const signupData = await signupResponse.json();
                console.log('✅ Signup endpoint: Working');
                console.log('New user created:', signupData.email);
            } else {
                console.log('❌ Signup endpoint failed:', signupResponse.status);
            }
            
        } else {
            console.log('❌ Backend connectivity failed:', healthResponse.status);
        }
        
        // Test 4: Check if React app is loaded
        console.log('\n4️⃣ Testing React app state...');
        if (window.React) {
            console.log('✅ React is loaded');
        } else {
            console.log('❌ React not found');
        }
        
        // Test 5: Check if auth store is available
        console.log('\n5️⃣ Testing auth store...');
        if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
            console.log('✅ React DevTools detected');
        }
        
        // Test 6: Check for any JavaScript errors
        console.log('\n6️⃣ Checking for JavaScript errors...');
        const errors = window.onerror || [];
        if (errors.length === 0) {
            console.log('✅ No JavaScript errors detected');
        } else {
            console.log('❌ JavaScript errors found:', errors);
        }
        
        console.log('\n🎉 Frontend authentication tests completed!');
        console.log('\n📋 Next steps:');
        console.log('1. Navigate to sign-in page and test login form');
        console.log('2. Test sign-up page and registration form');
        console.log('3. Test protected route redirection');
        console.log('4. Test logout functionality');
        
    } catch (error) {
        console.error('❌ Test failed with error:', error);
    }
}

// Auto-run the test
testFrontendAuth();
